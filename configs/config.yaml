global:
  # Please do not play with global values if you are not sure what you are doing
  log_level: debug  # Logging level: debug, info, warning, error
  # Word source configuration
  word_source:
    local_file: "data/words.gob"
    use_api_fallback: true
    connection_timeout: 30      # Timeout połączenia w sekundach
  nick_api:
    url: 'https://i.got.al/words.php'  # Deprecated - ISON and nick catching functionality has been removed
    max_word_length: 12  # Deprecated - ISON and nick catching functionality has been removed
    timeout: 5  # Timeout for API requests in seconds
  owner_command_prefixes:
    - "!"
    - "."
    - "@"
  reconnect_retries: 3
  reconnect_interval: 5
  mass_command_cooldown: 5
  bot_start_delay: 2
  connection_timeout: 20      # Timeout połączenia w sekundach
  proxy_mode: true
  max_reconnect_attempts: 3   # Maksymalna liczba prób reconnectu
  min_reconnect_delay: 3      # Minimalny czas między próbami (sekundy)
  max_reconnect_delay: 30     # Maksymalny czas między próbami (sekundy)
  master_bot:
    enabled: true
    nick: "tahioN"
    username: "tahio"
    realname: "Proxy Master Bot"
    server: ""  # Je<PERSON><PERSON> puste, zostanie wybrany losowy serwer
    channels:
      - "#tahio"
    use_ipv6: true  # Próbuj użyć IPv6, jeśli niedostępne, użyj IPv4

# Proxy mode settings
# You must place your proxy list in data/proxy.txt in format:
# socks5://user:pass@host:port
# *********************:port
# socks4://host:port

channels:
  - "#rr"  # Default channel to join

owner_command_prefixes:
  - "!"
  - "."
  - "@"

bots:
  - server: poznan.irc.pl #example server
    port: 6667
    ssl: false
    vhost: ************ # example IPv4
  - server: poznan.irc.pl #example server
    port: 6667
    ssl: false
    vhost: ************ # example IPv4