---
description: AI rules derived by <PERSON><PERSON><PERSON><PERSON> from the project AI interaction history
globs: *
---

## HEADERS

### PROJECT NAME
phantom <PERSON><PERSON> (pNb)

### PURPOSE
To provide a modular and extensible AI coding assistant rules file for maintaining and evolving the pNb project. This document outlines project rules, coding standards, workflow guidelines, references, documentation structures, and best practices.

### VERSIONING
The rules file is a living document, evolving with project needs. Updates are made by the designated expert software engineer and are based on user-AI interactions. Each update aims to clarify, refine, or add new rules and best practices.

## GENERAL RULES
*   Adhere to the coding standards and guidelines outlined in this document.
*   Follow the defined workflow and release rules.
*   When in Agent mode, operate methodically, step by step.
*   When asked to generate content or code, use colorText, colorCommand, and boldText to improve readability in the client's interface.

## TECH STACK
*   Go
*   go-ircevo (IRC library)
*   YAML (Configuration files)
*   SOCKS4, SOCKS5, HTTP (Proxy support)
*   AES-GCM (Encryption)
*   github.com/kofany/go-ircevo
*   github.com/kardianos/osext
*   github.com/h12w/go-socks5
*   github.com/phayes/freeport

## CODING STANDARDS
*   All code must be well-documented and easy to understand.
*   Use descriptive names for variables, functions, and methods.
*   Follow Go's style guidelines (e.g., using `gofmt`, `golint`).
*   Keep functions short and focused on a single task.
*   Handle errors explicitly and avoid `panic` unless absolutely necessary.
*   Use mutexes to protect shared resources from race conditions.

## PROJECT DOCUMENTATION & CONTEXT SYSTEM
*   Maintain up-to-date documentation in the project's `README.md` file.
*   Use SpecStory to generate documentation from user stories and AI interactions.
*   Document all changes to the AI coding assistant rules file, including the rationale for each change.
*   Configuration files are in YAML format.

## WORKFLOW & RELEASE RULES
*   Use feature branches for development.
*   All code changes must be reviewed before merging.
*   Tag releases with semantic versioning.

## DEBUGGING
*   Use the `util.Debug`, `util.Info`, `util.Warning`, and `util.Error` functions for logging.
*   Add logging to key functions and code paths to help diagnose issues.
*   When debugging, provide detailed information about the state of the application, including variable values, function arguments, and return values.
*   When troubleshooting, examine the logs for any errors or warnings.

## DEPLOYMENT
*   Deployment procedures will be documented in the project's `README.md` file.

## VALIDATION
*   Implement unit tests to verify the correctness of individual functions and methods.
*   Create integration tests to ensure that the different components of the application work together correctly.

## THIRD-PARTY LIBRARIES
*   Use third-party libraries judiciously and only when they provide significant value.
*   Ensure that all third-party libraries are properly licensed and compatible with the project's license.
*   Keep third-party libraries up-to-date to address security vulnerabilities and improve performance.

## SECURITY
*   Sanitize all user input to prevent injection attacks.
*   Use strong encryption algorithms to protect sensitive data.
*   Follow security best practices when handling passwords and other credentials.

## IRC COMMANDS
*   All IRC commands must be documented in this rules file.
*   Commands should be easy to use and understand.
*   Provide helpful error messages to users when they enter invalid commands.
*   Command names should be lowercase.

## DCC COMMANDS
*   All DCC commands must be documented in this rules file.
*   Commands should be easy to use and understand.
*   Provide helpful error messages to users when they enter invalid commands.
*   Command names should be lowercase.
*   Use colorText, colorCommand, and boldText to improve readability in the client's interface.

Available commands:
```
[ Basic ] commands (one bot):
.msg <target> <message> - Send a message
.join <channel> - Join a channel
.part <channel> - Leave a channel
.mode <target> [modes] - Set channel/user modes
.kick <channel> <user> [reason] - Kick a user
.quit - Disconnect and close the session
.nick <new_nick> - Change nickname
.raw <command> - Send raw IRC command

[ Mass ] commands (all bots):
.mjoin <channel> - All bots join a channel
.mpart <channel> - All bots leave a channel
.mreconnect - Reconnect all bots

[ Admin ] commands:
.addnick <nick> - Add a nick to monitor
.delnick <nick> - Remove a nick from monitoring
.listnicks - List monitored nicks
.addowner <mask> - Add an owner mask
.delowner <mask> - Remove an owner mask
.listowners - List current owners
.ison <on|off> - Enable/disable ISON monitoring

[ Info ] commands:
.info - Display system information
.bots - Show status of all bots
.servers - Show servers statistics
.abots - Display all bots status

[ Advanced ] commands:
.cflo <channel> <loops> <message> - Channel flood test
.nflo <nick> <loops> <message> - Nick flood test

For more help on a specific command, type: .help <command>
```

## PROXY MODE
*   The application must support proxy mode.
*   Proxy settings should be configurable via the `configs/config.yaml` file.
*   The proxy list is loaded from `data/proxy.txt`. Each line should be a proxy URL.
*   Supported proxy types: SOCKS4, SOCKS5, HTTP.

## MASTERBOT
*   The application must have a master bot that can be used to control all other bots.
*   The master bot should connect to a random IRC server on startup.
*   The master bot should join the #tahio channel.
*   The master bot should have a DCC interface for receiving commands.
*   The master bot should be able to add and remove owners.
*   The master bot should be able to send commands to all other bots.
*   The master bot configuration is defined in the `config.yaml` file under the `global.master_bot` section.
*   The master bot must use the IPv6 address of the server if available. If IPv6 is not available, the master bot should fall back to IPv4.
*  The master bot is a control point and will not participate in any actions.
*  All connections to master bot must be DCC connections
*  Masterbot is configured with a specialized DCC handler for controlling all the proxy connected bots.

## DRONE BOT
*   All bots created by proxy mode except the MasterBot are considered "drone" bots
*   Drone bots are lightweight and have a minimal feature set.
*   Drone bots connect to IRC servers via proxy and forward messages to the tunnel.
*   Drone bots do not need to be configured with DCC or owner lists.
*   Drone bots are only controlled by the MasterBot.

## .ADD BOTS COMMAND
*   Implemented by the MasterBot. 
*   Loads additional drone bots (as per the proxy lists) into the botnet. 
*   The proxy list is loaded from a file passed as an argument to the command, from the folder where the PNB was launched.

## ERROR HANDLING
*   Handle all errors gracefully and log them to a file.
*   Avoid panics unless absolutely necessary.
*   Use `defer recover()` to catch any panics that do occur.
*   Categorize errors and handle them accordingly (e.g., temporary errors, permanent errors).
*   In proxy mode, handle proxy-specific errors gracefully and log them to a separate file.
*   Implement proper shutdown procedures to ensure that all resources are released when the application exits.
*   Implement rate limiting and flood protection mechanisms to prevent abuse.

## NEW RULES

*   When creating and starting drone bots, make sure that all configuration is the same as upon first launch.
*   All actions must be applied to responsive bots

## REMOVED FUNCTIONALITY

*   ISON and nick catching functionality has been completely removed.
*   The BNC package and related functionalities have been removed.