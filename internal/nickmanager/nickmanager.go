package nickmanager

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"

	"github.com/kofany/pNb/internal/types"
	"github.com/kofany/pNb/internal/util"
)

type NickManager struct {
	nicks []string
	bots  []types.Bot
	mutex sync.Mutex
}

type NicksData struct {
	Nicks []string `json:"nicks"`
}

func NewNickManager() *NickManager {
	nm := &NickManager{
		nicks: make([]string, 0),
		bots:  make([]types.Bot, 0),
	}

	util.Info("NickManager started - nick catching functionality has been removed")

	return nm
}

func (nm *NickManager) LoadNicks(filename string) error {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()

	data, err := os.ReadFile(filename)
	if err != nil {
		return err
	}

	var nicksData NicksData
	if err := json.Unmarshal(data, &nicksData); err != nil {
		return err
	}

	nm.nicks = nicksData.Nicks
	return nil
}

// Start is now a stub function as ISON monitoring has been removed
func (nm *NickManager) Start() {
	// This function is intentionally left empty as the nick monitoring functionality has been removed
	util.Info("NickManager.Start() called but the nick monitoring functionality has been removed")
}

func (nm *NickManager) RegisterBot(bot types.Bot) {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()
	nm.bots = append(nm.bots, bot)
}

func (nm *NickManager) SetBots(bots []types.Bot) {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()
	nm.bots = bots
}

func (nm *NickManager) AddNick(nick string) error {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()

	// Check if nick already exists
	for _, n := range nm.nicks {
		if n == nick {
			return fmt.Errorf("nick '%s' already exists", nick)
		}
	}

	// Add nick to the list
	nm.nicks = append(nm.nicks, nick)

	// Save to file
	return nm.saveNicksToFile()
}

func (nm *NickManager) RemoveNick(nick string) error {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()

	// Find and remove nick
	index := -1
	for i, n := range nm.nicks {
		if n == nick {
			index = i
			break
		}
	}

	if index == -1 {
		return fmt.Errorf("nick '%s' not found", nick)
	}

	nm.nicks = append(nm.nicks[:index], nm.nicks[index+1:]...)

	// Save to file
	return nm.saveNicksToFile()
}

func (nm *NickManager) GetNicks() []string {
	nm.mutex.Lock()
	defer nm.mutex.Unlock()

	nicksCopy := make([]string, len(nm.nicks))
	copy(nicksCopy, nm.nicks)
	return nicksCopy
}

func (nm *NickManager) saveNicksToFile() error {
	data := NicksData{
		Nicks: nm.nicks,
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile("data/nicks.json", jsonData, 0644)
}

// Stub methods to maintain interface compatibility
func (nm *NickManager) EnableISON() {
	// ISON functionality has been removed
	util.Info("EnableISON called but ISON functionality has been removed")
}

func (nm *NickManager) DisableISON() {
	// ISON functionality has been removed
	util.Info("DisableISON called but ISON functionality has been removed")
}

func (nm *NickManager) IsISONEnabled() bool {
	// ISON functionality has been removed, always return false
	return false
}

// These methods now do nothing but are kept for interface compatibility
func (nm *NickManager) MarkServerNoLetters(serverName string) {
	// Nick catching functionality has been removed
}

func (nm *NickManager) NotifyNickChange(oldNick, newNick string) {
	// Nick catching functionality has been removed
}

// GetCurrentNickStatus implements the NickManager interface
func (nm *NickManager) GetCurrentNickStatus() []types.NickStatus {
	// Nick status monitoring has been removed, returning empty slice
	return []types.NickStatus{}
}

// GetNicksToCatch implements the NickManager interface
func (nm *NickManager) GetNicksToCatch() []string {
	// Nick catching functionality has been removed, returning empty slice
	return []string{}
}

// ReturnNickToPool implements the NickManager interface
func (nm *NickManager) ReturnNickToPool(nick string) {
	// Nick catching functionality has been removed
}

// MarkNickAsTemporarilyUnavailable implements the NickManager interface
func (nm *NickManager) MarkNickAsTemporarilyUnavailable(nick string) {
	// Nick catching functionality has been removed
}
