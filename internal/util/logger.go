package util

import (
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
)

type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARNING
	ERROR
)

var (
	logger      *log.Logger
	logLevel    LogLevel
	logMutex    sync.Mutex
	logFile     *os.File
	initialized bool
)

// InitLogger initializes the logger with the specified level and filename.
// If devMode is true, logs will only be printed to console and not saved to a file.
func InitLogger(level LogLevel, filename string, devMode bool) error {
	logMutex.Lock()
	defer logMutex.Unlock()

	if initialized {
		return fmt.Errorf("logger is already initialized")
	}

	logLevel = level
	initialized = true

	// W trybie deweloperskim nie zapisujemy do pliku
	if devMode {
		// Używamy standardowego wyjścia zamiast pliku
		logger = log.New(os.Stdout, "", log.LstdFlags)
		return nil
	}

	// W trybie produkcyjnym zapisujemy do pliku
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	logger = log.New(file, "", log.LstdFlags)
	logFile = file
	return nil
}

// CloseLogger closes the log file if it exists.
func CloseLogger() {
	logMutex.Lock()
	defer logMutex.Unlock()

	if initialized && logFile != nil {
		logFile.Close()
		logFile = nil
	}
	initialized = false
}

// logMessage logs a message at the specified level.
func logMessage(level LogLevel, format string, args ...interface{}) {
	logMutex.Lock()
	defer logMutex.Unlock()

	if !initialized || level < logLevel {
		return
	}

	msg := fmt.Sprintf(format, args...)
	prefix := ""
	switch level {
	case DEBUG:
		prefix = "DEBUG: "
	case INFO:
		prefix = "INFO: "
	case WARNING:
		prefix = "WARNING: "
	case ERROR:
		prefix = "ERROR: "
	}

	// Zapisz do loggera (plik lub stdout w trybie dev)
	if logger != nil {
		logger.SetPrefix(prefix)
		logger.Println(msg)
	}

	// W trybie dev nie drukujemy dwa razy na konsolę
	if logFile != nil {
		// Drukuj na konsolę tylko jeśli używamy pliku (nie w trybie dev)
		fmt.Println(prefix + msg)
	}
}

// Debug logs a message at DEBUG level.
func Debug(format string, args ...interface{}) {
	logMessage(DEBUG, format, args...)
}

// Info logs a message at INFO level.
func Info(format string, args ...interface{}) {
	logMessage(INFO, format, args...)
}

// Warning logs a message at WARNING level.
func Warning(format string, args ...interface{}) {
	logMessage(WARNING, format, args...)
}

// Error logs a message at ERROR level.
func Error(format string, args ...interface{}) {
	logMessage(ERROR, format, args...)
}

// ParseLogLevel parses the log level from a string.
func ParseLogLevel(levelStr string) (LogLevel, error) {
	switch strings.ToLower(levelStr) {
	case "debug":
		return DEBUG, nil
	case "info":
		return INFO, nil
	case "warning":
		return WARNING, nil
	case "error":
		return ERROR, nil
	default:
		return INFO, fmt.Errorf("unknown log level: %s", levelStr)
	}
}
