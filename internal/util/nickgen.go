package util

import (
	"crypto/tls"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

type APIResponse struct {
	Words []string `json:"words"`
}

type WordPoolConfig struct {
	ApiURL        string
	MaxWordLength int
	Timeout       int
	BatchSize     int
	MinPoolSize   int
	RetryAttempts int
	RetryDelay    time.Duration
}

// LocalWordSource reprezentuje lokalne źródło słów
type LocalWordSource struct {
	words []string
	mu    sync.RWMutex
}

// WordPool reprezentuje pulę słów z obsługą lokalnego źródła
type WordPool struct {
	config      WordPoolConfig
	words       []string
	usedWords   map[string]bool
	mu          sync.RWMutex
	localSource *LocalWordSource
}

// LoadWordsFromGob wczytuje słowa z pliku .gob
func LoadWordsFromGob(filepath string) (*LocalWordSource, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return nil, fmt.Errorf("could not open words file: %v", err)
	}
	defer file.Close()

	var words []string
	decoder := gob.NewDecoder(file)
	if err := decoder.Decode(&words); err != nil {
		return nil, fmt.Errorf("error decoding words: %v", err)
	}

	return &LocalWordSource{
		words: words,
	}, nil
}

// GetRandomWords zwraca określoną liczbę losowych słów z lokalnego źródła
func (lws *LocalWordSource) GetRandomWords(count int) ([]string, error) {
	lws.mu.RLock()
	defer lws.mu.RUnlock()

	if len(lws.words) < count {
		return nil, fmt.Errorf("not enough words available (have %d, need %d)", len(lws.words), count)
	}

	// Utwórz kopię slice'a indeksów
	indices := make([]int, len(lws.words))
	for i := range indices {
		indices[i] = i
	}

	// Przetasuj indeksy
	rand.Shuffle(len(indices), func(i, j int) {
		indices[i], indices[j] = indices[j], indices[i]
	})

	// Wybierz pierwsze count słów
	result := make([]string, count)
	for i := 0; i < count; i++ {
		result[i] = lws.words[indices[i]]
	}

	return result, nil
}

// NewWordPool tworzy nową instancję WordPool
func NewWordPool(config WordPoolConfig) *WordPool {
	wp := &WordPool{
		config:    config,
		words:     make([]string, 0),
		usedWords: make(map[string]bool),
	}

	// Inicjalizacja lokalnego źródła słów
	localSource, err := LoadWordsFromGob(filepath.Join("data", "words.gob"))
	if err != nil {
		Error("Failed to load local word source: %v", err)
	} else {
		wp.localSource = localSource
		Debug("Local word source initialized successfully")
	}

	return wp
}

// GetWordsFromAPI pobiera słowa najpierw z lokalnego źródła, a jeśli to się nie uda, z API
func GetWordsFromAPI(apiURL string, maxWordLength, timeout, count int, wp *WordPool) ([]string, error) {
	// Najpierw spróbuj użyć lokalnego źródła
	if wp != nil && wp.localSource != nil {
		words, err := wp.localSource.GetRandomWords(count)
		if err == nil {
			Debug("Successfully got %d words from local source", count)
			return words, nil
		}
		Debug("Failed to get words from local source: %v, falling back to API", err)
	}

	// Fallback do API
	client := &http.Client{
		Timeout: time.Duration(timeout) * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	url := fmt.Sprintf("%s?count=%d&length=%d", apiURL, count, maxWordLength)
	resp, err := client.Get(url)
	if err != nil {
		return generateFallbackWords(count, wp), nil
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return generateFallbackWords(count, wp), nil
	}

	var response APIResponse
	err = json.Unmarshal(body, &response)
	if err != nil || len(response.Words) < count {
		return generateFallbackWords(count, wp), nil
	}

	return response.Words[:count], nil
}

// EnsurePoolSize zapewnia minimalną wielkość puli słów
func (wp *WordPool) EnsurePoolSize(requiredSize int) error {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	currentSize := len(wp.words)
	if currentSize >= requiredSize {
		return nil
	}

	neededWords := requiredSize - currentSize

	// Najpierw spróbuj użyć lokalnego źródła
	if wp.localSource != nil {
		words, err := wp.localSource.GetRandomWords(neededWords)
		if err == nil {
			for _, word := range words {
				if !wp.usedWords[word] {
					wp.words = append(wp.words, word)
				}
			}
			Debug("Successfully added %d words from local source", len(words))
			return nil
		}
		Debug("Failed to get words from local source: %v, falling back to batched approach", err)
	}

	batchesNeeded := (neededWords + wp.config.BatchSize - 1) / wp.config.BatchSize

	for i := 0; i < batchesNeeded; i++ {
		batchSize := wp.config.BatchSize
		if i == batchesNeeded-1 {
			remainingNeeded := neededWords - (i * wp.config.BatchSize)
			if remainingNeeded < batchSize {
				batchSize = remainingNeeded
			}
		}

		var newWords []string
		var err error

		for attempt := 0; attempt < wp.config.RetryAttempts; attempt++ {
			newWords, err = GetWordsFromAPI(
				wp.config.ApiURL,
				wp.config.MaxWordLength,
				wp.config.Timeout,
				batchSize,
				wp,
			)

			if err == nil {
				break
			}

			if attempt < wp.config.RetryAttempts-1 {
				time.Sleep(wp.config.RetryDelay)
			}
		}

		if err != nil {
			return fmt.Errorf("failed to get words after %d attempts: %v",
				wp.config.RetryAttempts, err)
		}

		for _, word := range newWords {
			if !wp.usedWords[word] {
				wp.words = append(wp.words, word)
			}
		}
	}

	return nil
}

// GetUniqueWords pobiera unikalne słowa z puli
func (wp *WordPool) GetUniqueWords(count int) ([]string, error) {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	if err := wp.ensurePoolSizeUnlocked(count); err != nil {
		return nil, fmt.Errorf("failed to ensure pool size: %v", err)
	}
	result := make([]string, 0, count)
	for i := 0; i < count && len(wp.words) > 0; i++ {
		// Pobierz słowo z końca listy
		word := wp.words[len(wp.words)-1]
		wp.words = wp.words[:len(wp.words)-1]

		// Oznacz jako użyte
		wp.usedWords[word] = true
		result = append(result, word)
	}

	// Jeśli pula jest poniżej minimalnego rozmiaru, rozpocznij uzupełnianie w tle
	if len(wp.words) < wp.config.MinPoolSize {
		GoSafe(func() {
			if err := wp.RefreshPool(); err != nil {
				Error("Failed to refresh word pool: %v", err)
			}
		})
	}

	return result, nil
}

// RefreshPool odświeża pulę słów
func (wp *WordPool) RefreshPool() error {
	return wp.EnsurePoolSize(wp.config.MinPoolSize)
}

// MarkWordAsUsed oznacza słowo jako użyte
func (wp *WordPool) MarkWordAsUsed(word string) {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	wp.usedWords[word] = true

	// Usuń słowo z puli jeśli tam jest
	for i, w := range wp.words {
		if w == word {
			wp.words = append(wp.words[:i], wp.words[i+1:]...)
			break
		}
	}
}

// ReturnWordToPool zwraca słowo do puli
func (wp *WordPool) ReturnWordToPool(word string) {
	wp.mu.Lock()
	defer wp.mu.Unlock()

	// Jeśli słowo nie jest oznaczone jako użyte, nie rób nic
	if !wp.usedWords[word] {
		return
	}

	// Usuń z używanych i dodaj z powrotem do puli
	delete(wp.usedWords, word)
	wp.words = append(wp.words, word)
}

// GetPoolStats zwraca statystyki puli
func (wp *WordPool) GetPoolStats() (total, available, used int) {
	wp.mu.RLock()
	defer wp.mu.RUnlock()

	return len(wp.words) + len(wp.usedWords), len(wp.words), len(wp.usedWords)
}

// ensurePoolSizeUnlocked jest wewnętrzną wersją EnsurePoolSize bez blokady
func (wp *WordPool) ensurePoolSizeUnlocked(requiredSize int) error {
	currentSize := len(wp.words)
	if currentSize >= requiredSize {
		return nil
	}

	neededWords := requiredSize - currentSize

	// Najpierw spróbuj użyć lokalnego źródła
	if wp.localSource != nil {
		words, err := wp.localSource.GetRandomWords(neededWords)
		if err == nil {
			for _, word := range words {
				if !wp.usedWords[word] {
					wp.words = append(wp.words, word)
				}
			}
			return nil
		}
		Debug("Failed to get words from local source (unlocked): %v", err)
	}

	batchesNeeded := (neededWords + wp.config.BatchSize - 1) / wp.config.BatchSize

	for i := 0; i < batchesNeeded; i++ {
		batchSize := wp.config.BatchSize
		if i == batchesNeeded-1 {
			remainingNeeded := neededWords - (i * wp.config.BatchSize)
			if remainingNeeded < batchSize {
				batchSize = remainingNeeded
			}
		}

		var newWords []string
		var err error

		for attempt := 0; attempt < wp.config.RetryAttempts; attempt++ {
			newWords, err = GetWordsFromAPI(
				wp.config.ApiURL,
				wp.config.MaxWordLength,
				wp.config.Timeout,
				batchSize,
				wp,
			)

			if err == nil {
				break
			}

			if attempt < wp.config.RetryAttempts-1 {
				time.Sleep(wp.config.RetryDelay)
			}
		}

		if err != nil {
			return fmt.Errorf("failed to get words after %d attempts: %v",
				wp.config.RetryAttempts, err)
		}

		for _, word := range newWords {
			if !wp.usedWords[word] {
				wp.words = append(wp.words, word)
			}
		}
	}

	return nil
}

// ValidateWord sprawdza, czy słowo jest odpowiednie do użycia jako nick
func (wp *WordPool) ValidateWord(word string) bool {
	word = strings.TrimSpace(word)
	return len(word) >= 3 &&
		len(word) <= wp.config.MaxWordLength &&
		isAlpha(word)
}

// GetRandomWord pobiera losowe słowo z puli
func (wp *WordPool) GetRandomWord() (string, error) {
	words, err := wp.GetUniqueWords(1)
	if err != nil {
		return "", err
	}
	if len(words) == 0 {
		return "", fmt.Errorf("no words available")
	}
	return words[0], nil
}

// Funkcje pomocnicze
func generateFallbackWords(count int, wp *WordPool) []string {
	words := make([]string, count)
	for i := 0; i < count; i++ {
		words[i] = GenerateFallbackNick(wp)
	}
	return words
}

func isAlpha(s string) bool {
	for _, r := range s {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z')) {
			return false
		}
	}
	return true
}

// GenerateFallbackNick generuje nick w przypadku gdy nie można pobrać słowa z żadnego źródła
func GenerateFallbackNick(wp *WordPool) string {
	// Jeśli mamy dostęp do puli, najpierw próbujemy z niej
	if wp != nil {
		// Najpierw sprawdźmy rozmiar puli
		total, available, _ := wp.GetPoolStats()
		Debug("WordPool stats before get word - Total: %d, Available: %d", total, available)

		// Jeśli pula jest pusta, próbujemy ją uzupełnić
		if available == 0 {
			Debug("WordPool is empty, trying to refresh from local source")
			if wp.localSource != nil {
				if words, err := wp.localSource.GetRandomWords(1); err == nil {
					Debug("Got word from local source: %s", words[0])
					return words[0]
				}
			}
			// Jeśli nie udało się z lokalnego źródła, próbujemy odświeżyć
			if err := wp.RefreshPool(); err != nil {
				Debug("Failed to refresh pool: %v", err)
			}
		}

		// Próbujemy pobrać słowo kilka razy
		for attempts := 0; attempts < 3; attempts++ {
			word, err := wp.GetRandomWord()
			if err == nil && word != "" && len(word) >= 3 {
				Debug("Successfully got word from pool (attempt %d): %s", attempts+1, word)
				return word
			}
			Debug("Failed to get word from pool (attempt %d): %v", attempts+1, err)

			// Po każdej nieudanej próbie odświeżamy pulę
			if err := wp.RefreshPool(); err != nil {
				Debug("Failed to refresh pool after attempt %d: %v", attempts+1, err)
			}
			time.Sleep(time.Millisecond * 100) // Krótkie opóźnienie między próbami
		}

		Debug("All attempts to get word from pool failed")
	} else {
		Debug("WordPool is nil")
	}

	// Jeśli wszystkie próby zawiodły, generujemy losowy nick
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	length := rand.Intn(5) + 5
	nick := make([]byte, length)
	for i := range nick {
		nick[i] = charset[rand.Intn(len(charset))]
	}
	randomNick := string(nick)
	Debug("Generated random nick: %s", randomNick)
	return randomNick
}
