package util

import "time"

// ExponentialBackoff implementuje mechanizm wykładniczego opóźnienia
type ExponentialBackoff struct {
	Initial  time.Duration // Początkowe opóźnienie
	Max      time.Duration // Maksymalne opóźnienie
	Factor   float64       // Mnożnik dla kolejnych opóźnień
	MaxCount int           // Maksymalna liczba prób
	count    int           // Aktualna liczba prób
}

// NextDelay zwraca następne opóźnienie z wykładniczym wzrostem
func (b *ExponentialBackoff) NextDelay() time.Duration {
	if b.count >= b.MaxCount {
		return b.Max
	}

	delay := b.Initial
	for i := 0; i < b.count; i++ {
		delay = time.Duration(float64(delay) * b.Factor)
		if delay > b.Max {
			delay = b.Max
			break
		}
	}

	b.count++
	return delay
}

// Reset resetuje licznik prób
func (b *ExponentialBackoff) Reset() {
	b.count = 0
}
