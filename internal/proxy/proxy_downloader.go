package proxy

import (
	"bufio"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/kofany/pNb/internal/util"
)

// ProxyDownloader pobiera i przetwarza listy proxy z zewnętrznych źródeł
type ProxyDownloader struct {
	iLineLookup *ILineLookup
	httpClient  *http.Client
	mutex       sync.Mutex
}

// NewProxyDownloader tworzy nową instancję ProxyDownloader
func NewProxyDownloader() (*ProxyDownloader, error) {
	dbPath := filepath.Join("data", "ip_entries.gob")
	lookup, err := NewILineLookup(dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize i-line lookup: %v", err)
	}

	// Klient HTTP z timeoutem
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	return &ProxyDownloader{
		iLineLookup: lookup,
		httpClient:  client,
	}, nil
}

// LoadProxySources ładuje źródła proxy z pliku
func (pd *ProxyDownloader) LoadProxySources(proxyType ProxyType) ([]string, error) {
	var filename string
	switch proxyType {
	case Socks4:
		filename = "data/proxy_sources_socks4.txt"
	case Socks5:
		filename = "data/proxy_sources_socks5.txt"
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxyType)
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open proxy sources file: %v", err)
	}
	defer file.Close()

	var sources []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		source := strings.TrimSpace(scanner.Text())
		if source != "" && !strings.HasPrefix(source, "#") {
			sources = append(sources, source)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading proxy sources file: %v", err)
	}

	return sources, nil
}

// DownloadProxies pobiera proxy z podanego URL
func (pd *ProxyDownloader) DownloadProxies(sourceURL string) (string, error) {
	util.Debug("Downloading proxies from: %s", sourceURL)

	// Tworzymy request
	req, err := http.NewRequest("GET", sourceURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	// Ustawiamy User-Agent, aby uniknąć blokowania
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// Wykonujemy request
	resp, err := pd.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to download proxies: %v", err)
	}
	defer resp.Body.Close()

	// Sprawdzamy status odpowiedzi
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("bad response status: %s", resp.Status)
	}

	// Odczytujemy zawartość
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}

	return string(body), nil
}

// ParseProxies parsuje pobrane listy proxy
func (pd *ProxyDownloader) ParseProxies(content string, proxyType ProxyType) ([]ProxyEntry, error) {
	var proxies []ProxyEntry

	// Dzielimy zawartość na linie
	scanner := bufio.NewScanner(strings.NewReader(content))
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		// Sprawdzamy, czy linia zawiera IP:PORT
		parts := strings.Split(line, ":")
		if len(parts) < 2 {
			continue
		}

		// Sprawdzamy, czy IP jest poprawne
		ip := parts[0]
		if net.ParseIP(ip) == nil {
			continue
		}

		// Tworzymy URL proxy
		proxyURL := fmt.Sprintf("%s://%s", proxyType, line)
		proxy, err := pd.parseProxyLine(proxyURL)
		if err != nil {
			continue
		}

		proxies = append(proxies, proxy)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error scanning proxy content: %v", err)
	}

	return proxies, nil
}

// parseProxyLine parsuje linię proxy w formacie URL
func (pd *ProxyDownloader) parseProxyLine(line string) (ProxyEntry, error) {
	u, err := url.Parse(line)
	if err != nil {
		return ProxyEntry{}, err
	}

	proxyType := ProxyType(strings.ToLower(u.Scheme))
	host, port, err := net.SplitHostPort(u.Host)
	if err != nil {
		return ProxyEntry{}, err
	}

	proxy := ProxyEntry{
		Type: proxyType,
		Host: host,
		Port: port,
	}

	if u.User != nil {
		proxy.Username = u.User.Username()
		proxy.Password, _ = u.User.Password()
	}

	return proxy, nil
}

// CheckAndFilterProxies sprawdza i filtruje proxy
func (pd *ProxyDownloader) CheckAndFilterProxies(proxies []ProxyEntry) []ProxyEntry {
	// Mapa do deduplikacji proxy
	uniqueProxies := make(map[string]ProxyEntry)

	for _, proxy := range proxies {
		// Klucz do deduplikacji
		key := fmt.Sprintf("%s:%s", proxy.Host, proxy.Port)

		// Sprawdzamy, czy proxy już istnieje
		if _, exists := uniqueProxies[key]; exists {
			continue
		}

		// Sprawdzamy i-line dla proxy
		servers := pd.iLineLookup.GetServersForIP(proxy.Host)
		proxy.Servers = servers

		// Dodajemy proxy do mapy
		uniqueProxies[key] = proxy
	}

	// Konwertujemy mapę na slice
	var result []ProxyEntry
	for _, proxy := range uniqueProxies {
		result = append(result, proxy)
	}

	return result
}

// DownloadAndProcessProxies pobiera i przetwarza proxy z wszystkich źródeł
func (pd *ProxyDownloader) DownloadAndProcessProxies(proxyType ProxyType) ([]ProxyEntry, error) {
	pd.mutex.Lock()
	defer pd.mutex.Unlock()

	// Ładujemy źródła proxy
	sources, err := pd.LoadProxySources(proxyType)
	if err != nil {
		return nil, fmt.Errorf("failed to load proxy sources: %v", err)
	}

	util.Info("Loaded %d proxy sources for type %s", len(sources), proxyType)

	var allProxies []ProxyEntry
	var wg sync.WaitGroup
	proxyChan := make(chan []ProxyEntry, len(sources))
	errorChan := make(chan error, len(sources))

	// Pobieramy proxy z każdego źródła równolegle
	for _, source := range sources {
		wg.Add(1)
		util.GoSafe(func() {
			defer wg.Done()

			// Pobieramy proxy
			content, err := pd.DownloadProxies(source)
			if err != nil {
				errorChan <- fmt.Errorf("failed to download proxies from %s: %v", source, err)
				return
			}

			// Parsujemy proxy
			proxies, err := pd.ParseProxies(content, proxyType)
			if err != nil {
				errorChan <- fmt.Errorf("failed to parse proxies from %s: %v", source, err)
				return
			}

			proxyChan <- proxies
		})
	}

	// Czekamy na zakończenie wszystkich goroutines
	wg.Wait()
	close(proxyChan)
	close(errorChan)

	// Zbieramy błędy
	for err := range errorChan {
		util.Warning("Proxy download error: %v", err)
	}

	// Zbieramy proxy
	for proxies := range proxyChan {
		allProxies = append(allProxies, proxies...)
	}

	util.Info("Downloaded %d proxies of type %s", len(allProxies), proxyType)

	// Filtrujemy i deduplikujemy proxy
	filteredProxies := pd.CheckAndFilterProxies(allProxies)

	util.Info("After filtering, got %d unique proxies of type %s", len(filteredProxies), proxyType)

	return filteredProxies, nil
}
