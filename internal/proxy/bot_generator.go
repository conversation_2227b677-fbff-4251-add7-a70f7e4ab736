package proxy

import (
	"sync"
	"time"

	"github.com/kofany/pNb/internal/config"
	"github.com/kofany/pNb/internal/util"
)

type BotGenerator struct {
	openServers  []string
	globalConfig *config.GlobalConfig
	wordPool     *util.WordPool
}

func NewBotGenerator(globalConfig *config.GlobalConfig) *BotGenerator {
	wordPoolConfig := util.WordPoolConfig{
		ApiURL:        globalConfig.NickAPI.URL,
		MaxWordLength: globalConfig.NickAPI.MaxWordLength,
		Timeout:       globalConfig.NickAPI.Timeout,
		BatchSize:     999,
		MinPoolSize:   3000,
		RetryAttempts: 5,
		RetryDelay:    time.Second * 2,
	}

	return &BotGenerator{
		globalConfig: globalConfig,
		wordPool:     util.NewWordPool(wordPoolConfig),
		openServers: []string{
			"irc.atw-inter.net",
			"ircnet.clue.be",
			"tngnet.ircnet.io",
			"irc.nlnog.net",
			"irc.psychz.net",
			"tempest.ircnet.io",
			"irc.swepipe.net",
			"openirc.snt.utwente.nl",
			"irc.us.ircnet.net",
		},
	}
}

func (bg *BotGenerator) GenerateBotsConfigs(proxies []ProxyEntry) ([]config.BotConfig, error) {
	start := time.Now()
	var botsConfigs []config.BotConfig
	openServerIndex := 0

	workerCount := 16 // liczba workerów, można dostosować do liczby CPU
	jobs := make(chan ProxyEntry, len(proxies))
	results := make(chan []config.BotConfig, len(proxies))
	var wg sync.WaitGroup

	// Worker pool
	for w := 0; w < workerCount; w++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for proxy := range jobs {
				var localConfigs []config.BotConfig
				if len(proxy.Servers) > 0 && proxy.Servers[0] != "NoMatch" {
					for _, server := range proxy.Servers {
						localConfigs = append(localConfigs, bg.createBotConfig(proxy, server, 2))
						util.Debug("[WORKER] Created bot for matched server %s through proxy %s", server, proxy.Host)
					}
				}
				// Open server dla każdego proxy
				bgOpenServers := bg.openServers
				if len(bgOpenServers) == 0 {
					results <- localConfigs
					continue
				}
				// Ustal openServerIndex lokalnie, by uniknąć wyścigów
				openServer := bgOpenServers[openServerIndex%len(bgOpenServers)]
				openServerIndex++
				localConfigs = append(localConfigs, bg.createBotConfig(proxy, openServer, 2))
				util.Debug("[WORKER] Created bot for open server %s through proxy %s", openServer, proxy.Host)
				results <- localConfigs
			}
		}()
	}

	for _, proxy := range proxies {
		jobs <- proxy
	}
	close(jobs)

	go func() {
		wg.Wait()
		close(results)
	}()

	for batch := range results {
		botsConfigs = append(botsConfigs, batch...)
	}

	// Oblicz całkowitą liczbę potrzebnych słów (3 na bota: nick, username, realname)
	totalWordsNeeded := len(botsConfigs) * 3

	if err := bg.wordPool.EnsurePoolSize(totalWordsNeeded); err != nil {
		util.Error("Failed to ensure word pool size: %v", err)
		// Nie przerywamy - będziemy używać fallback nicków jeśli to konieczne
	}

	total, available, used := bg.wordPool.GetPoolStats()
	util.Info("Word pool stats - Total: %d, Available: %d, Used: %d", total, available, used)
	util.Info("[PERF] GenerateBotsConfigs finished in %v for %d proxy", time.Since(start), len(proxies))

	return botsConfigs, nil
}

func (bg *BotGenerator) createBotConfig(proxy ProxyEntry, server string, maxAttempts int) config.BotConfig {
	// Pobierz 3 unikalne słowa dla bota
	words, err := bg.wordPool.GetUniqueWords(3)
	if err != nil {
		util.Error("Failed to get unique words for bot config: %v", err)
		// Używamy fallback nicków w przypadku błędu, przekazując wordPool
		words = []string{
			util.GenerateFallbackNick(bg.wordPool),
			util.GenerateFallbackNick(bg.wordPool),
			util.GenerateFallbackNick(bg.wordPool),
		}
	}

	// Użyj słów jako nick, username i realname
	nick := words[0]
	username := words[1]
	realname := words[2]

	// Logowanie utworzenia nowej konfiguracji
	util.Debug("Creating bot config - Server: %s, Nick: %s, Username: %s, RealName: %s",
		server, nick, username, realname)

	return config.BotConfig{
		Server:      server,
		Port:        6667,
		SSL:         false,
		MaxAttempts: maxAttempts,
		Nick:        nick,
		Username:    username,
		RealName:    realname,
		Proxy: &config.ProxySettings{
			Type:     string(proxy.Type),
			Address:  proxy.Host + ":" + proxy.Port,
			Username: proxy.Username,
			Password: proxy.Password,
		},
	}
}

func (bg *BotGenerator) GetStats() (total, available, used int) {
	return bg.wordPool.GetPoolStats()
}
