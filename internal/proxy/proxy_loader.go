package proxy

import (
	"bufio"
	"net"
	"net/url"
	"os"
	"path/filepath"
	"strings"
)

type ProxyLoader struct {
	iLineLookup *ILineLookup
}

func NewProxyLoader() (*ProxyLoader, error) {
	dbPath := filepath.Join("data", "ip_entries.gob")
	lookup, err := NewILineLookup(dbPath)
	if err != nil {
		return nil, err
	}

	return &ProxyLoader{
		iLineLookup: lookup,
	}, nil
}

func (pl *ProxyLoader) LoadAndProcessProxies() ([]ProxyEntry, error) {
	proxyPath := filepath.Join("data", "proxy.txt")

	file, err := os.Open(proxyPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var proxies []ProxyEntry
	scanner := bufio.NewScanner(file)

	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" {
			continue
		}

		proxy, err := pl.parseProxyLine(line)
		if err != nil {
			continue
		}

		servers := pl.iLineLookup.GetServersForIP(proxy.Host)
		proxy.Servers = servers
		proxies = append(proxies, proxy)
	}

	return proxies, scanner.Err()
}

func (pl *ProxyLoader) parseProxyLine(line string) (ProxyEntry, error) {
	u, err := url.Parse(line)
	if err != nil {
		return ProxyEntry{}, err
	}

	proxyType := ProxyType(strings.ToLower(u.Scheme))
	host, port, err := net.SplitHostPort(u.Host)
	if err != nil {
		return ProxyEntry{}, err
	}

	proxy := ProxyEntry{
		Type: proxyType,
		Host: host,
		Port: port,
	}

	if u.User != nil {
		proxy.Username = u.User.Username()
		proxy.Password, _ = u.User.Password()
	}

	return proxy, nil
}
