package proxy

import (
	"encoding/gob"
	"net"
	"os"
)

type ServerE<PERSON>ry struct {
	Server  string
	Network *net.IPNet
}

type ILineLookup struct {
	entries []ServerEntry
}

func NewILineLookup(dbPath string) (*ILineLookup, error) {
	file, err := os.Open(dbPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var entries []ServerEntry
	decoder := gob.NewDecoder(file)
	if err := decoder.Decode(&entries); err != nil {
		return nil, err
	}

	return &ILineLookup{entries: entries}, nil
}

func (il *ILineLookup) GetServersForIP(ipStr string) []string {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return []string{"InvalidIP"}
	}

	var matchingServers []string
	for _, entry := range il.entries {
		if entry.Network.Contains(ip) {
			matchingServers = append(matchingServers, entry.Server)
		}
	}

	if len(matchingServers) == 0 {
		return []string{"NoMatch"}
	}

	return matchingServers
}
