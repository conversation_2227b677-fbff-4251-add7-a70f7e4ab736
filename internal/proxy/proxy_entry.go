package proxy

type ProxyType string

const (
	Socks4 ProxyType = "socks4"
	Socks5 ProxyType = "socks5"
	HTTP   ProxyType = "http"
)

type ProxyEntry struct {
	Type     ProxyType
	Host     string
	Port     string
	Username string
	Password string
	Servers  []string // Lista dostępnych serwerów dla tego proxy
}

type BotConfig struct {
	ProxyEntry ProxyEntry
	Server     string
	Attempts   int
}

var OpenServers = []string{
	"irc.atw-inter.net",
	"ircnet.clue.be",
	"tngnet.ircnet.io",
	"irc.nlnog.net",
	"irc.spadhausen.com",
	"irc.psychz.net",
	"tempest.ircnet.io",
	"irc.swepipe.net",
	"openirc.snt.utwente.nl",
	"irc.us.ircnet.net",
}
