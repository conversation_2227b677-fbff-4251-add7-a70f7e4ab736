// internal/types/interfaces.go
package types

import (
	"time"

	irc "github.com/kofany/go-ircevo"
	"github.com/kofany/pNb/internal/auth"
	"github.com/kofany/pNb/internal/config"
	"github.com/kofany/pNb/internal/proxy"
	"github.com/kofany/pNb/internal/util"
)

type WordPoolManager interface {
	GetUniqueWords(count int) ([]string, error)
	ReturnWordToPool(word string)
	MarkWordAsUsed(word string)
	GetPoolStats() (total, available, used int)
}

type Bot interface {
	// Usunięto lub oznaczono jako przestarzałe metody związane z ISON:
	// AttemptNickChange(nick string) - deprecated
	// RequestISON(nicks []string) ([]string, error) - deprecated

	GetCurrentNick() string
	IsConnected() bool
	IsOnChannel(channel string) bool // Nowa metoda do sprawdzania, czy bot jest na kanale
	SetOwnerList(owners auth.OwnerList)
	SetChannels(channels []string)
	Connect() error
	Quit(message string)
	Reconnect()
	SendMessage(target, message string)
	JoinChannel(channel string)
	PartChannel(channel string)
	ChangeNick(newNick string)
	HandleCommands(e *irc.Event)
	SetBotManager(manager BotManager)
	GetBotManager() BotManager
	SetNickManager(manager NickManager)
	GetNickManager() NickManager
	GetServerName() string
	SendRaw(message string)
	GetMaxAttempts() int
	UpdateOwners(owners []string) error
	IsMasterBot() bool

	// Metody zachowane dla kompatybilności, ale oznaczone jako przestarzałe:
	AttemptNickChange(nick string)                // Deprecated: Nick catching functionality has been removed
	RequestISON(nicks []string) ([]string, error) // Deprecated: ISON functionality has been removed
}

type NickManager interface {
	// Podstawowe funkcje do zachowania dla kompatybilności
	RegisterBot(bot Bot)
	SetBots(bots []Bot)
	AddNick(nick string) error
	RemoveNick(nick string) error
	GetNicks() []string
	Start() // Przestarzała, ale zachowana dla kompatybilności

	// Metody zachowane dla kompatybilności, ale oznaczone jako przestarzałe:
	ReturnNickToPool(nick string)                 // Deprecated: Nick catching functionality has been removed
	GetNicksToCatch() []string                    // Deprecated: Nick catching functionality has been removed
	MarkNickAsTemporarilyUnavailable(nick string) // Deprecated: Nick catching functionality has been removed
	NotifyNickChange(oldNick, newNick string)     // Deprecated: Nick catching functionality has been removed
	MarkServerNoLetters(serverName string)        // Deprecated: Nick catching functionality has been removed
	EnableISON()                                  // Deprecated: ISON functionality has been removed
	DisableISON()                                 // Deprecated: ISON functionality has been removed
	IsISONEnabled() bool                          // Deprecated: ISON functionality has been removed
	GetCurrentNickStatus() []NickStatus           // Deprecated: Nick catching functionality has been removed
}

type BotManager interface {
	StartBots()
	Stop()
	CanExecuteMassCommand(cmdName string) bool
	AddOwner(ownerMask string) error
	RemoveOwner(ownerMask string) error
	GetOwners() []string
	GetBots() []Bot
	GetNickManager() NickManager
	SetMassCommandCooldown(duration time.Duration)
	GetMassCommandCooldown() time.Duration
	CollectReactions(channel, message string, action func() error)
	SendSingleMsg(channel, message string)
	GetTotalCreatedBots() int
	GetWordPool() *util.WordPool
	GetSessionTunnel(sessionID string) (interface{}, error)
	UpdateOwners(owners []string) error
	GetGlobalConfig() *config.GlobalConfig
	GetBotsStatus() []NodeBotStatus

	// Methods for proxy management
	AddNewBots(proxyEntries []proxy.ProxyEntry) (int, error)
	GetWordPoolStats() (total, available, used int)
	StartBotsAndWait(bots []Bot) int

	// Methods for bot cleanup
	PerformCleanup()

	// Methods for reconnection storm prevention
	DetectMassDisconnect() bool
	HandleNetworkOutage()
	TrackDisconnect(bot Bot)
}

type ReactionRequest struct {
	Channel   string
	Message   string
	Timestamp time.Time
	Action    func() error
}

// Struktura zachowana dla kompatybilności z istniejącym kodem
type NickStatus struct {
	Nick       string    `json:"nick"`
	Server     string    `json:"server"`
	Status     string    `json:"status"`
	LastUpdate time.Time `json:"last_update"`
}

// Interface for tunnel creation
type DCCTunnelCreator interface {
	// Zdefiniuj konkretne typy zwracane przez metody - musimy użyć interface{}
	// ze względu na ograniczenia importu cyklicznego
	CreateMasterDCCTunnel(bot Bot, ownerNick string, botManager BotManager) interface{}
	CreateStandardDCCTunnel(bot Bot, ownerNick string, onStop func()) interface{}
}

type BotStatus struct {
	Nick        string    `json:"nick"`
	Server      string    `json:"server"`
	ConnectedAt time.Time `json:"connected_at"`
	Status      string    `json:"status"`
}

var startTime = time.Now()

func GetUptime() int64 {
	return int64(time.Since(startTime).Seconds())
}

type NodeBotStatus struct {
	NodeID    string    `json:"node_id"`
	BotNick   string    `json:"bot_nick"`
	Server    string    `json:"server"`
	Connected bool      `json:"connected"`
	LastSeen  time.Time `json:"last_seen"`
}

type NodeStats struct {
	NodeID      string    `json:"node_id"`
	IP          string    `json:"ip"`
	PID         int       `json:"pid"`
	ConnectedAt time.Time `json:"connected_at"`
	LastPing    time.Time `json:"last_ping"`
	PingLatency int64     `json:"ping_latency"`
}

type ServerStats struct {
	Server    string `json:"server"`
	TotalBots int    `json:"total_bots"`
}
