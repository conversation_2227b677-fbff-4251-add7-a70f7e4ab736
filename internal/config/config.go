package config

import (
	"fmt"
	"os"

	"github.com/fatih/color"
	"gopkg.in/yaml.v2"
)

type Config struct {
	Global   GlobalConfig `yaml:"global"`
	Bots     []BotConfig  `yaml:"bots"`
	Channels []string     `yaml:"channels"`
}

type GlobalConfig struct {
	LogLevel        string   `yaml:"log_level"`
	CommandPrefixes []string `yaml:"owner_command_prefixes"`
	WordSource      struct {
		UseLocal      bool `yaml:"use_local"`
		FallbackToAPI bool `yaml:"fallback_to_api"`
	} `yaml:"word_source"`
	NickAPI              NickAPI         `yaml:"nick_api"`
	Channels             []string        `yaml:"channels"`
	ReconnectRetries     int             `yaml:"reconnect_retries"`
	ReconnectInterval    int             `yaml:"reconnect_interval"`
	MassCommandCooldown  int             `yaml:"mass_command_cooldown"`
	BotStartDelay        int             `yaml:"bot_start_delay"`
	ConnectionTimeout    int             `yaml:"connection_timeout"`
	MaxReconnectAttempts int             `yaml:"max_reconnect_attempts"`
	MinReconnectDelay    int             `yaml:"min_reconnect_delay"`
	MaxReconnectDelay    int             `yaml:"max_reconnect_delay"`
	MasterBot            MasterBotConfig `yaml:"master_bot"`
}

type NickAPI struct {
	URL           string `yaml:"url"`
	MaxWordLength int    `yaml:"max_word_length"`
	Timeout       int    `yaml:"timeout"`
}

type BotConfig struct {
	Server      string         `yaml:"server"`
	Port        int            `yaml:"port"`
	SSL         bool           `yaml:"ssl"`
	Vhost       string         `yaml:"vhost"`
	Proxy       *ProxySettings `yaml:"proxy,omitempty"`
	MaxAttempts int            `yaml:"max_attempts"`
	Nick        string         `yaml:"nick"`
	Username    string         `yaml:"username"`
	RealName    string         `yaml:"realname"`
}

type ProxySettings struct {
	Type     string `yaml:"type"`
	Address  string `yaml:"address"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
}

// MasterBotConfig defines configuration for the master bot in proxy mode
type MasterBotConfig struct {
	Enabled  bool     `yaml:"enabled"`
	Nick     string   `yaml:"nick"`
	Username string   `yaml:"username"`
	RealName string   `yaml:"realname"`
	Server   string   `yaml:"server"`
	Channels []string `yaml:"channels"`
	UseIPv6  bool     `yaml:"use_ipv6"`
}

func LoadConfig(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		return nil, err
	}

	return &config, nil
}

func (cfg *BotConfig) ServerAddress() string {
	return fmt.Sprintf("%s:%d", cfg.Server, cfg.Port)
}

func CheckAndCreateConfigFiles() error {
	folders := []string{"configs", "data"}
	files := map[string]string{
		"configs/config.yaml": `global:
  # Please do not play with global values if you are not sure what you are doing
  log_level: warning  # Logging level: debug, info, warning, error
  # Word source configuration
  word_source:
    use_local: true  # Use local words.gob file
    fallback_to_api: false  # Don't use API as fallback
  nick_api:  # deprecated - kept for compatibility
    url: 'https://i.got.al/words.php'
    max_word_length: 12
    timeout: 5  # Timeout for API requests in seconds
  owner_command_prefixes:
    - "!"
    - "."
    - "@"
  reconnect_retries: 3
  reconnect_interval: 2
  mass_command_cooldown: 5
  bot_start_delay: 2
  connection_timeout: 30
  max_reconnect_attempts: 5
  min_reconnect_delay: 2
  max_reconnect_delay: 60
  master_bot:
    enabled: true
    nick: "tahioN"
    username: "tahio"
    realname: "Proxy Master Bot"
    server: ""  # If empty, a random open server will be selected
    channels:
      - "#tahio"
    use_ipv6: true  # Use IPv6 for MasterBot connection

# Proxy mode settings
# You must place your proxy list in data/proxy.txt in format:
# socks5://user:pass@host:port
# *********************:port
# socks4://host:port

channels:
  - "#tahio"  # Default channel to join

owner_command_prefixes:
  - "!"
  - "."
  - "@"`,
		"configs/owners.json": `{
  "owners": [
    "*!*ident@hostname"
  ]
}`,
		"data/nicks.json": `{
  "nicks": [
    "CoolBot",
    "NickKeeper",
    "IRCGuardian",
    "NetWatcher"
  ]
}`,
	}

	requiredFiles := []string{"data/words.gob"}

	missingItems := []string{}
	cyan := color.New(color.FgCyan).SprintFunc()
	green := color.New(color.FgGreen).SprintFunc()
	red := color.New(color.FgRed).SprintFunc()
	yellow := color.New(color.FgYellow).SprintFunc()

	fmt.Println(cyan("Checking core files:"))

	// Check and create folders
	for _, folder := range folders {
		fmt.Printf("%-25s", cyan(folder))
		if _, err := os.Stat(folder); os.IsNotExist(err) {
			if err := os.MkdirAll(folder, 0755); err != nil {
				fmt.Println(red("[ ERROR ]"))
				return fmt.Errorf("failed to create folder %s: %v", folder, err)
			}
			missingItems = append(missingItems, folder)
			fmt.Println(yellow("[ CREATED ]"))
		} else {
			fmt.Println(green("[ OK ]"))
		}
	}

	// Check and create files
	for file, content := range files {
		fmt.Printf("%-25s", cyan(file))
		if _, err := os.Stat(file); os.IsNotExist(err) {
			if err := os.WriteFile(file, []byte(content), 0644); err != nil {
				fmt.Println(red("[ ERROR ]"))
				return fmt.Errorf("failed to create file %s: %v", file, err)
			}
			missingItems = append(missingItems, file)
			fmt.Println(yellow("[ CREATED ]"))
		} else {
			fmt.Println(green("[ OK ]"))
		}
	}

	// Check required files that must exist
	for _, file := range requiredFiles {
		fmt.Printf("%-25s", cyan(file))
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Println(red("[ MISSING ]"))
			fmt.Println("\n" + red("Error: Required file missing: "+file))
			fmt.Println(yellow("\nPlease generate the words.gob file using the word list converter tool:"))
			fmt.Println(cyan("1. Place your word list in data/words.txt"))
			fmt.Println(cyan("2. Run the converter tool: go run build.go -convert-words"))
			return fmt.Errorf("required file missing: %s", file)
		} else {
			fmt.Println(green("[ OK ]"))
		}
	}

	if len(missingItems) > 0 {
		fmt.Println("\n" + yellow("The following items were missing and have been created with example content:"))
		for _, item := range missingItems {
			fmt.Printf("- %s\n", cyan(item))
		}
		fmt.Println("\n" + yellow("Please edit these files with your desired configuration before running the bot again."))
		fmt.Println(red("Exiting the program."))
		os.Exit(1)
	}

	fmt.Println("\n" + green("All necessary folders and files are present."))
	return nil
}
