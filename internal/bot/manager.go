package bot

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/kofany/pNb/internal/auth"
	"github.com/kofany/pNb/internal/config"
	"github.com/kofany/pNb/internal/dcc"
	"github.com/kofany/pNb/internal/proxy"
	"github.com/kofany/pNb/internal/types"
	"github.com/kofany/pNb/internal/util"
)

// Ensure BotManager implements types.BotManager
var _ types.BotManager = (*BotManager)(nil)

// BotManager manages multiple IRC bots
type BotManager struct {
	bots                []types.Bot
	owners              auth.OwnerList
	wg                  sync.WaitGroup
	stopChan            chan struct{}
	nickManager         types.NickManager
	commandBotIndex     int
	mutex               sync.RWMutex
	lastMassCommand     map[string]time.Time
	massCommandCooldown time.Duration
	reactionRequests    map[string]types.ReactionRequest
	reactionMutex       sync.Mutex
	ctx                 context.Context
	cancel              context.CancelFunc
	errorHandled        bool
	errorMutex          sync.Mutex
	globalConfig        *config.GlobalConfig
	wordPool            *util.WordPool
	totalCreatedBots    int
	heartbeatStop       chan struct{}
	dccTunnels          map[string]*dcc.DCCTunnel
	dccTunnelsMu        sync.RWMutex
	masterBot           types.Bot
	// For reconnection storm prevention
	disconnectTimes []time.Time
	disconnectMutex sync.Mutex
}

func (bm *BotManager) GetSessionTunnel(sessionID string) (interface{}, error) {
	bm.dccTunnelsMu.RLock()
	defer bm.dccTunnelsMu.RUnlock()

	if tunnel, exists := bm.dccTunnels[sessionID]; exists {
		return tunnel, nil
	}
	return nil, fmt.Errorf("no tunnel found for session %s", sessionID)
}

// NewBotManager creates a new bot manager for proxy mode
func NewBotManager(cfg *config.Config, owners auth.OwnerList, nm types.NickManager, proxyBotsConfigs []config.BotConfig) *BotManager {
	ctx, cancel := context.WithCancel(context.Background())

	wordPoolConfig := util.WordPoolConfig{
		ApiURL:        cfg.Global.NickAPI.URL,
		MaxWordLength: cfg.Global.NickAPI.MaxWordLength,
		Timeout:       cfg.Global.NickAPI.Timeout,
		BatchSize:     999,
		MinPoolSize:   3000,
		RetryAttempts: 3,
		RetryDelay:    time.Second * 2,
	}
	wordPool := util.NewWordPool(wordPoolConfig)

	requiredWords := len(proxyBotsConfigs) * 4
	if err := wordPool.EnsurePoolSize(requiredWords); err != nil {
		util.Error("Failed to ensure word pool size: %v", err)
		os.Exit(1)
	}

	manager := &BotManager{
		bots:                make([]types.Bot, len(proxyBotsConfigs)),
		totalCreatedBots:    len(proxyBotsConfigs),
		owners:              owners,
		stopChan:            make(chan struct{}),
		nickManager:         nm,
		lastMassCommand:     make(map[string]time.Time),
		massCommandCooldown: time.Duration(cfg.Global.MassCommandCooldown) * time.Second,
		reactionRequests:    make(map[string]types.ReactionRequest),
		ctx:                 ctx,
		cancel:              cancel,
		errorHandled:        false,
		errorMutex:          sync.Mutex{},
		globalConfig:        &cfg.Global,
		wordPool:            wordPool,
		heartbeatStop:       make(chan struct{}),
		dccTunnels:          make(map[string]*dcc.DCCTunnel),
		dccTunnelsMu:        sync.RWMutex{},
	}

	for i, botCfg := range proxyBotsConfigs {
		manager.bots[i] = NewBot(&botCfg, &cfg.Global, nm, manager)
	}
	nm.SetBots(manager.bots)

	total, available, used := wordPool.GetPoolStats()
	util.Info("Word pool initialized - Total: %d, Available: %d, Used: %d",
		total, available, used)

	go manager.startHeartbeat()
	go manager.cleanupDisconnectedBots()

	// Initialize MasterBot if enabled
	if cfg.Global.MasterBot.Enabled {
		util.Info("Initializing MasterBot for proxy mode")
		err := manager.InitMasterBot(cfg.Global.MasterBot)
		if err != nil {
			util.Warning("Failed to initialize MasterBot: %v", err)
		} else {
			util.Info("MasterBot initialized successfully")
		}
	}

	return manager
}

// StartBots starts all bots and connects them to their servers
func (bm *BotManager) StartBots() {
	// Uruchamiamy boty
	botChannel := make(chan types.Bot, len(bm.bots))
	for _, bot := range bm.bots {
		botChannel <- bot
	}

	var wg sync.WaitGroup
	for i := 0; i < len(bm.bots); i++ {
		wg.Add(1)
		util.GoSafe(func() {
			defer wg.Done()
			for bot := range botChannel {
				// Always use limited retries as we always use proxy mode
				bm.startBotWithLimitedRetries(bot)

				// Opóźnienie przed uruchomieniem kolejnego bota
				select {
				case <-bm.ctx.Done():
					return
				case <-time.After(time.Duration(bm.globalConfig.BotStartDelay) * time.Second):
				}
			}
		})
	}

	close(botChannel)
	wg.Wait()
}

func (bm *BotManager) startBotWithLimitedRetries(bot types.Bot) {
	maxAttempts := bot.GetMaxAttempts()
	retryDelay := time.Duration(bm.globalConfig.BotStartDelay) * time.Second

	// Set drone mode for regular bots (if it's a *Bot)
	if concreteBot, ok := bot.(*Bot); ok && !concreteBot.IsMaster {
		concreteBot.isDrone = true
		util.Debug("Marked bot %s as drone", concreteBot.GetCurrentNick())
	}

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		select {
		case <-bm.ctx.Done():
			return
		default:
			util.Debug("Starting connection attempt %d/%d for bot %s",
				attempt, maxAttempts, bot.GetCurrentNick())

			err := bot.Connect()
			if err == nil {
				util.Info("Bot %s connected successfully through proxy", bot.GetCurrentNick())
				// Usunięto informowanie botnet o nowym bocie
				return
			}

			util.Warning("Connection attempt %d/%d failed for bot %s: %v",
				attempt, maxAttempts, bot.GetCurrentNick(), err)

			if attempt == maxAttempts {
				util.Error("Failed to connect proxy bot %s after %d attempts. Removing bot.",
					bot.GetCurrentNick(), maxAttempts)
				// Zwracamy słowa do puli przed usunięciem bota
				if b, ok := bot.(*Bot); ok {
					if bm.wordPool != nil {
						bm.wordPool.ReturnWordToPool(b.CurrentNick)
						bm.wordPool.ReturnWordToPool(b.Username)
						bm.wordPool.ReturnWordToPool(b.Realname)
					}
				}
				bm.removeBot(bot)
				// Usunięto informowanie botnet o usuniętym bocie
				return
			}

			util.Debug("Waiting %d seconds before next connection attempt for bot %s",
				bm.globalConfig.BotStartDelay, bot.GetCurrentNick())

			select {
			case <-time.After(retryDelay):
			case <-bm.ctx.Done():
				return
			}
		}
	}
}

func (bm *BotManager) Stop() {
	// Loguj liczbę gorutyn przed zamykaniem
	numBefore := runtime.NumGoroutine()
	util.Info("[SHUTDOWN] Goroutines before shutdown: %d", numBefore)

	// Zatrzymaj wszystkie inne komponenty
	bm.cancel()
	close(bm.stopChan)
	bm.wg.Wait()

	// Zatrzymaj heartbeat
	if bm.heartbeatStop != nil {
		close(bm.heartbeatStop)
	}

	// Zatrzymaj tunele DCC
	bm.dccTunnelsMu.Lock()
	for _, tunnel := range bm.dccTunnels {
		tunnel.Stop()
	}
	bm.dccTunnels = make(map[string]*dcc.DCCTunnel)
	bm.dccTunnelsMu.Unlock()

	// Zwróć słowa do puli i zatrzymaj boty
	bm.mutex.Lock()
	for _, bot := range bm.bots {
		if b, ok := bot.(*Bot); ok {
			if bm.wordPool != nil {
				bm.wordPool.ReturnWordToPool(b.CurrentNick)
				bm.wordPool.ReturnWordToPool(b.Username)
				bm.wordPool.ReturnWordToPool(b.Realname)
			}
		}
		// Wyślij komunikat o zamknięciu i zamknij bota
		bot.Quit("Shutting down")
	}
	bm.mutex.Unlock()

	// Czekaj na zamknięcie wszystkich połączeń
	time.Sleep(2 * time.Second)

	// Dodatkowe wymuszone czekanie na zamknięcie gorutyn
	time.Sleep(3 * time.Second)
	numAfter := runtime.NumGoroutine()
	util.Info("[SHUTDOWN] Goroutines after shutdown: %d", numAfter)
	if numAfter > numBefore/2+10 { // tolerancja na gorutyny systemowe
		util.Warning("[SHUTDOWN] Goroutines did not drop as expected! Possible leak: before=%d after=%d", numBefore, numAfter)
	}

	util.Info("BotManager shutdown completed successfully.")
}

func (bm *BotManager) removeBot(botToRemove types.Bot) {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	var newBots []types.Bot
	for _, bot := range bm.bots {
		if bot != botToRemove {
			newBots = append(newBots, bot)
		} else {
			// Zamknij połączenie przed usunięciem
			bot.Quit("Removing bot due to fatal error")

			// Zwróć słowa do puli - korzystamy z metody bota, która obsługuje wszystkie zabezpieczenia
			if b, ok := bot.(*Bot); ok {
				b.mutex.Lock()
				b.gaveUp = true
				b.mutex.Unlock()

				// Zwracamy słowa z użyciem bezpiecznej metody
				b.returnWordsToPool()
			}
		}
	}

	bm.bots = newBots
	bm.nickManager.SetBots(bm.bots)

	util.Info("Bot removed from manager, current bot count: %d", len(bm.bots))
}

func (bm *BotManager) CanExecuteMassCommand(cmdName string) bool {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	lastExecution, exists := bm.lastMassCommand[cmdName]
	if !exists || time.Since(lastExecution) > bm.massCommandCooldown {
		bm.lastMassCommand[cmdName] = time.Now()
		util.Debug("BotManager: Mass command %s can be executed", cmdName)
		return true
	}

	util.Debug("BotManager: Mass command %s is on cooldown", cmdName)
	return false
}

// GetWordPool returns the WordPool instance for use by bots
func (bm *BotManager) GetWordPool() *util.WordPool {
	return bm.wordPool
}

// RefreshWordPool ensures the word pool has enough words
func (bm *BotManager) RefreshWordPool(minSize int) error {
	if bm.wordPool == nil {
		return fmt.Errorf("word pool not initialized")
	}
	return bm.wordPool.EnsurePoolSize(minSize)
}
func (bm *BotManager) AddOwner(ownerMask string) error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	for _, owner := range bm.owners.Owners {
		if owner == ownerMask {
			return fmt.Errorf("owner '%s' already exists", ownerMask)
		}
	}

	bm.owners.Owners = append(bm.owners.Owners, ownerMask)
	return bm.saveOwnersToFile()
}

func (bm *BotManager) RemoveOwner(ownerMask string) error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	index := -1
	for i, owner := range bm.owners.Owners {
		if owner == ownerMask {
			index = i
			break
		}
	}

	if index == -1 {
		return fmt.Errorf("owner '%s' not found", ownerMask)
	}

	bm.owners.Owners = append(bm.owners.Owners[:index], bm.owners.Owners[index+1:]...)
	return bm.saveOwnersToFile()
}

func (bm *BotManager) GetOwners() []string {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	ownersCopy := make([]string, len(bm.owners.Owners))
	copy(ownersCopy, bm.owners.Owners)
	return ownersCopy
}

func (bm *BotManager) saveOwnersToFile() error {
	jsonData, err := json.MarshalIndent(bm.owners, "", "  ")
	if err != nil {
		return err
	}

	err = os.WriteFile("configs/owners.json", jsonData, 0644)
	if err != nil {
		return err
	}

	for _, bot := range bm.bots {
		bot.SetOwnerList(bm.owners)
	}

	return nil
}

func (bm *BotManager) GetBots() []types.Bot {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	botsCopy := make([]types.Bot, len(bm.bots))
	copy(botsCopy, bm.bots)
	return botsCopy
}

func (bm *BotManager) GetNickManager() types.NickManager {
	return bm.nickManager
}

func (bm *BotManager) SetMassCommandCooldown(duration time.Duration) {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()
	bm.massCommandCooldown = duration
}

func (bm *BotManager) GetMassCommandCooldown() time.Duration {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()
	return bm.massCommandCooldown
}

func (bm *BotManager) CollectReactions(channel, message string, action func() error) {
	// Generujemy unikalny klucz dla tego żądania
	key := channel + ":" + message + ":" + fmt.Sprintf("%d", time.Now().UnixNano())
	now := time.Now()

	// Sprawdzamy duplikaty pod krótkim lockiem
	shouldProcess := true
	bm.reactionMutex.Lock()
	for existingKey, req := range bm.reactionRequests {
		// Sprawdzamy tylko klucze z tym samym kanałem i wiadomością (ignorując timestamp)
		if strings.HasPrefix(existingKey, channel+":"+message+":") && now.Sub(req.Timestamp) < 5*time.Second {
			shouldProcess = false
			util.Debug("Duplicate reaction request ignored: %s", channel+":"+message)
			break
		}
	}

	// Jeśli to duplikat, zwalniamy mutex i kończymy
	if !shouldProcess {
		bm.reactionMutex.Unlock()
		return
	}

	// Rejestrujemy nowe żądanie
	bm.reactionRequests[key] = types.ReactionRequest{
		Channel:   channel,
		Message:   message,
		Timestamp: now,
		Action:    action,
	}
	bm.reactionMutex.Unlock()

	// Uruchamiamy całą obsługę w osobnej goroutine, aby nie blokować wywołującego
	util.GoSafe(func() {
		// Dodajemy obsługę paniki na poziomie całej funkcji
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in CollectReactions: %v", r)
				// Próbujemy wysłać informację o błędzie
				bm.SendSingleMsg(channel, fmt.Sprintf("Internal error: %v", r))
				// Czyścimy żądanie
				go bm.cleanupReactionRequest(key)
			}
		}()

		// Wykonujemy akcję, jeśli została podana
		if action != nil {
			// Używamy kontekstu z timeoutem dla akcji - zwiększamy timeout do 30 sekund
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			// Kanał do sygnalizacji zakończenia akcji
			done := make(chan error, 1)

			// Uruchamiamy akcję w osobnej goroutine
			util.GoSafe(func() {
				defer func() {
					if r := recover(); r != nil {
						done <- fmt.Errorf("panic in action: %v", r)
					}
				}()
				done <- action()
			})

			// Czekamy na zakończenie akcji lub timeout
			var err error
			select {
			case err = <-done:
				// Akcja zakończona
				util.Debug("Action completed for %s", key)
			case <-ctx.Done():
				err = fmt.Errorf("action timed out after 30 seconds")
				util.Warning("Action timed out for %s", key)
			}

			// Obsługa błędów
			if err != nil {
				util.Error("Action error for %s: %v", key, err)

				// Używamy krótszego locka dla flagi błędu
				bm.errorMutex.Lock()
				shouldSendError := !bm.errorHandled
				bm.errorHandled = true
				bm.errorMutex.Unlock()

				// Wysyłamy komunikat o błędzie tylko raz
				if shouldSendError {
					errorMsg := fmt.Sprintf("Error: %v", err)
					bm.SendSingleMsg(channel, errorMsg)
				}

				// Czyścimy żądanie w przypadku błędu
				go bm.cleanupReactionRequest(key)
				return
			}
		}

		// Wysyłamy wiadomość, jeśli jest podana
		if message != "" {
			bm.SendSingleMsg(channel, message)
		}

		// Uruchamiamy czyszczenie po określonym czasie
		go bm.cleanupReactionRequest(key)
	})
}

func (bm *BotManager) cleanupReactionRequest(key string) {
	// Czekamy przed czyszczeniem
	time.Sleep(5 * time.Second)

	// Używamy osobnego mutexa dla operacji czyszczenia
	bm.reactionMutex.Lock()
	delete(bm.reactionRequests, key)
	bm.reactionMutex.Unlock()

	// Resetujemy flagę obsługi błędów w osobnej sekcji krytycznej
	bm.errorMutex.Lock()
	bm.errorHandled = false
	bm.errorMutex.Unlock()

	util.Debug("Cleaned up reaction request: %s", key)
}

func (bm *BotManager) SendSingleMsg(channel, message string) {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	if len(bm.bots) == 0 {
		return
	}
	bot := bm.bots[bm.commandBotIndex]
	bm.commandBotIndex = (bm.commandBotIndex + 1) % len(bm.bots)
	bot.SendMessage(channel, message)
}

// Metody pomocnicze do zarządzania pulą słów
func (bm *BotManager) GetWordPoolStats() (total, available, used int) {
	if bm.wordPool == nil {
		return 0, 0, 0
	}
	return bm.wordPool.GetPoolStats()
}

func (bm *BotManager) EnsureWordPoolSize(requiredSize int) error {
	if bm.wordPool == nil {
		return fmt.Errorf("word pool not initialized")
	}
	return bm.wordPool.EnsurePoolSize(requiredSize)
}

func (bm *BotManager) ReturnWordToPool(word string) {
	if bm.wordPool != nil {
		bm.wordPool.ReturnWordToPool(word)
		total, available, used := bm.wordPool.GetPoolStats()
		util.Debug("Word returned to pool - Word: %s, Total: %d, Available: %d, Used: %d",
			word, total, available, used)
	}
}
func (bm *BotManager) GetTotalCreatedBots() int {
	return bm.totalCreatedBots
}

// MarkBotAsActive oznacza bota jako aktywnego w BotManager
// Ta metoda jest używana do upewnienia się, że bot jest poprawnie zliczany
func (bm *BotManager) MarkBotAsActive(bot types.Bot) {
	// Nie musimy nic robić, ponieważ boty są już przechowywane w slice bm.bots
	// a metoda IsConnected() jest używana do sprawdzania, czy bot jest połączony
	// Metoda ta istnieje głównie dla jasności kodu i potencjalnych przyszłych rozszerzeń
	util.Debug("Bot %s marked as active in BotManager", bot.GetCurrentNick())
}

func (bm *BotManager) startHeartbeat() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			numGoroutine := runtime.NumGoroutine()
			var m runtime.MemStats
			runtime.ReadMemStats(&m)
			util.Info("[HEARTBEAT] goroutines=%d, mem=%dKB", numGoroutine, m.Alloc/1024)
		case <-bm.heartbeatStop:
			return
		}
	}
}
func (bm *BotManager) UpdateOwners(owners []string) error {
	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	bm.owners.Owners = owners
	return bm.saveOwnersToFile()
}

// Ulepszony cleanupDisconnectedBots, który wykonuje czyszczenie tylko raz po uruchomieniu
func (bm *BotManager) cleanupDisconnectedBots() {
	util.Debug("Starting one-time cleanup of disconnected bots")

	// Pierwsze czyszczenie po uruchomieniu
	firstCleanupDelay := 5 * time.Minute
	time.Sleep(firstCleanupDelay)

	// Wykonaj czyszczenie tylko raz
	bm.PerformCleanup()

	util.Info("One-time cleanup completed. Further cleanups will be triggered only by .clean command")
}

// PerformCleanup wykonuje czyszczenie wszystkich niepołączonych botów
func (bm *BotManager) PerformCleanup() {
	bm.mutex.Lock()

	var botsToRemove []types.Bot

	// Identyfikacja botów do usunięcia
	for _, bot := range bm.bots {
		if !bot.IsConnected() {
			util.Warning("Bot %s will be removed due to connection failure", bot.GetCurrentNick())
			botsToRemove = append(botsToRemove, bot)
		}
	}

	bm.mutex.Unlock()

	if len(botsToRemove) == 0 {
		util.Debug("No disconnected bots found during cleanup")
		return
	}

	// Używamy wspólnej metody do czyszczenia botów
	bm.cleanupSpecificBots(botsToRemove)
}

// cleanupSpecificBots czyści określone boty z puli
func (bm *BotManager) cleanupSpecificBots(botsToRemove []types.Bot) {
	if len(botsToRemove) == 0 {
		return
	}

	util.Debug("Cleaning up %d specific bots", len(botsToRemove))

	bm.mutex.Lock()
	defer bm.mutex.Unlock()

	// Usuwanie zidentyfikowanych botów
	var newBots []types.Bot
	for _, bot := range bm.bots {
		shouldKeep := true
		for _, removeBot := range botsToRemove {
			if bot == removeBot {
				shouldKeep = false

				// Zwracanie słów do puli używając bezpiecznej metody
				if b, ok := bot.(*Bot); ok {
					b.returnWordsToPool()
				}

				// Zamykanie połączenia
				bot.Quit("Removing inactive bot")
				break
			}
		}
		if shouldKeep {
			newBots = append(newBots, bot)
		}
	}

	bm.bots = newBots

	// Aktualizacja NickManager
	if bm.nickManager != nil {
		bm.nickManager.SetBots(bm.bots)
	}

	util.Info("Cleanup complete: removed %d bots. Remaining bots: %d",
		len(botsToRemove), len(bm.bots))
}

func (bm *BotManager) GetGlobalConfig() *config.GlobalConfig {
	return bm.globalConfig
}

// Nowa metoda GetBotsStatus, która zastępuje GetNetworkBotsStatus
func (bm *BotManager) GetBotsStatus() []types.NodeBotStatus {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	var statuses []types.NodeBotStatus
	localNodeID := "local" // Identyfikator dla lokalnego systemu

	for _, bot := range bm.bots {
		status := types.NodeBotStatus{
			NodeID:    localNodeID,
			BotNick:   bot.GetCurrentNick(),
			Server:    bot.GetServerName(),
			Connected: bot.IsConnected(),
			LastSeen:  time.Now(),
		}
		statuses = append(statuses, status)
	}

	return statuses
}

// DetectMassDisconnect checks if there have been multiple disconnects in a short time period
func (bm *BotManager) DetectMassDisconnect() bool {
	const threshold = 5                 // Number of disconnects to consider it a mass disconnect
	const timeWindow = 60 * time.Second // Time window to count disconnects

	bm.disconnectMutex.Lock()
	defer bm.disconnectMutex.Unlock()

	// Count recent disconnects
	now := time.Now()
	recentDisconnects := 0

	for _, disconnectTime := range bm.disconnectTimes {
		if now.Sub(disconnectTime) < timeWindow {
			recentDisconnects++
		}
	}

	// Add current disconnect
	bm.disconnectTimes = append(bm.disconnectTimes, now)

	// Clean up old entries
	newList := make([]time.Time, 0)
	for _, t := range bm.disconnectTimes {
		if now.Sub(t) < timeWindow {
			newList = append(newList, t)
		}
	}
	bm.disconnectTimes = newList

	return recentDisconnects >= threshold
}

// HandleNetworkOutage implements staggered reconnection with jitter to prevent reconnection storms
func (bm *BotManager) HandleNetworkOutage() {
	util.Warning("Network outage detected! Implementing staggered reconnection strategy")

	// Get all disconnected bots
	disconnectedBots := bm.getDisconnectedBots()
	totalBots := len(disconnectedBots)

	if totalBots == 0 {
		return
	}

	// Implementacja staggered reconnect
	util.Info("Starting staggered reconnection for %d bots", totalBots)

	// Parametry staggered reconnect
	baseDelay := time.Duration(bm.globalConfig.MinReconnectDelay) * time.Second
	maxDelay := time.Duration(bm.globalConfig.MaxReconnectDelay) * time.Second

	// Używamy kontekstu z timeoutem dla całej operacji
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Tworzymy grupę wait dla synchronizacji
	var wg sync.WaitGroup

	// Kanał do limitowania równoczesnych połączeń
	semaphore := make(chan struct{}, 5) // max 5 równoczesnych reconnectów

	for i, bot := range disconnectedBots {
		wg.Add(1)

		// Obliczamy opóźnienie dla tego bota
		delay := baseDelay + time.Duration(i)*(maxDelay-baseDelay)/time.Duration(totalBots)

		util.GoSafe(func() {
			defer wg.Done()

			// Czekamy na opóźnienie lub anulowanie kontekstu
			select {
			case <-time.After(delay):
				// Kontynuuj
			case <-ctx.Done():
				util.Warning("Reconnection for bot %s cancelled due to timeout", bot.GetCurrentNick())
				return
			}

			// Używamy semafora do ograniczenia równoczesnych połączeń
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			util.Info("Attempting reconnection for bot %s (%d/%d)",
				bot.GetCurrentNick(), i+1, totalBots)

			// Próbujemy ponownie połączyć bota
			if err := bot.Connect(); err != nil {
				util.Error("Failed to reconnect bot %s: %v", bot.GetCurrentNick(), err)

				// Próbujemy z nowym nickiem
				if bot, ok := bot.(*Bot); ok {
					newNick, username, realname := bot.getNewWordsFromPool()
					if newNick == "" {
						util.Error("Failed to get new words for bot: empty nick")
						return
					}

					bot.mutex.Lock()
					bot.CurrentNick = newNick
					bot.Username = username
					bot.Realname = realname
					bot.mutex.Unlock()

					util.Info("Retrying with new nick %s", newNick)

					if err := bot.Connect(); err != nil {
						util.Error("Failed to reconnect bot with new nick %s: %v", newNick, err)
					} else {
						util.Info("Successfully reconnected bot with new nick %s", newNick)
					}
				}
			} else {
				util.Info("Successfully reconnected bot %s", bot.GetCurrentNick())
			}
		})
	}

	// Czekamy na zakończenie wszystkich reconnectów lub timeout
	done := make(chan struct{})
	util.GoSafe(func() {
		wg.Wait()
		close(done)
	})

	select {
	case <-done:
		util.Info("Staggered reconnection completed for all bots")
	case <-ctx.Done():
		util.Warning("Staggered reconnection timed out")
	}
}

// Helper method to get disconnected bots
func (bm *BotManager) getDisconnectedBots() []types.Bot {
	bm.mutex.RLock()
	defer bm.mutex.RUnlock()

	var disconnectedBots []types.Bot
	for _, bot := range bm.bots {
		if !bot.IsConnected() {
			disconnectedBots = append(disconnectedBots, bot)
		}
	}

	return disconnectedBots
}

// InitMasterBot initializes the master bot for proxy mode
func (bm *BotManager) InitMasterBot(masterConfig config.MasterBotConfig) error {
	if !masterConfig.Enabled {
		util.Debug("MasterBot is disabled in config, skipping initialization")
		return nil
	}

	// Choose a random server if not specified
	server := masterConfig.Server
	if server == "" {
		// List of open servers (bez tempest.ircnet.io)
		openServers := []string{
			"irc.atw-inter.net",
			"ircnet.clue.be",
			"tngnet.ircnet.io",
			"irc.spadhausen.com",
			"irc.nlnog.net",
			"irc.psychz.net",
			"irc.swepipe.net",
			"openirc.snt.utwente.nl",
			"irc.us.ircnet.net",
		}

		// Pick a random server from the list (bez tempest.ircnet.io)
		server = openServers[rand.Intn(len(openServers))]

		// Logujemy wybrany serwer
		util.Info("Randomly selected server for MasterBot: %s", server)
	}

	// Determine which network interface to use (IPv6 or IPv4)
	var vhost string

	// First try the preferred protocol
	if masterConfig.UseIPv6 {
		// Try to get IPv6 address if preferred
		ipv6Addr, err := util.GetExternalIPByProtocol("tcp6")
		if err == nil && ipv6Addr != "" {
			vhost = ipv6Addr
			util.Info("Using IPv6 for MasterBot: %s", vhost)
		} else {
			// Fallback to IPv4 if IPv6 is not available
			util.Warning("IPv6 not available for MasterBot: %v", err)
			util.Info("Falling back to IPv4 for MasterBot")

			ipv4Addr, err := util.GetExternalIPByProtocol("tcp4")
			if err == nil && ipv4Addr != "" {
				vhost = ipv4Addr
				util.Info("Using IPv4 for MasterBot: %s", vhost)
			} else {
				util.Warning("IPv4 also not available for MasterBot: %v", err)
				util.Info("Continuing without specific vhost")
			}
		}
	} else {
		// Try to get IPv4 address if preferred
		ipv4Addr, err := util.GetExternalIPByProtocol("tcp4")
		if err == nil && ipv4Addr != "" {
			vhost = ipv4Addr
			util.Info("Using IPv4 for MasterBot: %s", vhost)
		} else {
			// Fallback to IPv6 if IPv4 is not available
			util.Warning("IPv4 not available for MasterBot: %v", err)
			util.Info("Falling back to IPv6 for MasterBot")

			ipv6Addr, err := util.GetExternalIPByProtocol("tcp6")
			if err == nil && ipv6Addr != "" {
				vhost = ipv6Addr
				util.Info("Using IPv6 for MasterBot: %s", vhost)
			} else {
				util.Warning("IPv6 also not available for MasterBot: %v", err)
				util.Info("Continuing without specific vhost")
			}
		}
	}

	// Create bot config
	botConfig := &config.BotConfig{
		Server:      server,
		Port:        6667,
		SSL:         false,
		MaxAttempts: 3,
		Nick:        masterConfig.Nick,
		Username:    masterConfig.Username,
		RealName:    masterConfig.RealName,
		Vhost:       vhost, // Set the vhost based on selected protocol
	}

	// Create the master bot as a concrete *Bot instance, not directly as types.Bot
	concreteMasterBot := NewBot(botConfig, bm.globalConfig, bm.nickManager, bm)

	// Mark as master bot and not a drone
	concreteMasterBot.IsMaster = true
	concreteMasterBot.isDrone = false
	util.Info("Configured MasterBot with IsMaster=true and isDrone=false")

	// Configure DCC handler for MasterBot
	concreteMasterBot.ConfigureMasterDCCHandler()

	// Store reference to master bot (as interface)
	bm.masterBot = concreteMasterBot

	// Set owner list and channels
	bm.masterBot.SetOwnerList(bm.owners)
	bm.masterBot.SetChannels(masterConfig.Channels)

	// Connect the master bot
	util.Info("Connecting MasterBot to %s as %s using vhost=%s", server, masterConfig.Nick, vhost)
	err := bm.masterBot.Connect()
	if err != nil {
		util.Error("Failed to connect MasterBot: %v", err)
		return err
	}

	util.Info("MasterBot connected successfully to %s", server)
	return nil
}

// AddNewBots dodaje nowe boty do istniejącej puli na podstawie listy proxy
func (bm *BotManager) AddNewBots(proxyEntries []proxy.ProxyEntry) (int, error) {
	util.Info("Adding new bots from %d proxy entries", len(proxyEntries))

	// Sprawdzamy, czy mamy wystarczającą liczbę słów w puli
	requiredWords := len(proxyEntries) * 3 // 3 słowa na bota (nick, username, realname)
	if err := bm.wordPool.EnsurePoolSize(requiredWords); err != nil {
		util.Warning("Failed to ensure word pool size: %v", err)
		// Kontynuujemy mimo to, będziemy używać fallbackowych nicków
	}

	// Generowanie konfiguracji botów
	botGenerator := proxy.NewBotGenerator(bm.globalConfig)
	botsConfigs, err := botGenerator.GenerateBotsConfigs(proxyEntries)
	if err != nil {
		return 0, fmt.Errorf("failed to generate bot configurations: %v", err)
	}

	// Tworzenie nowych botów
	newBots := make([]types.Bot, len(botsConfigs))
	for i, botCfg := range botsConfigs {
		newBots[i] = NewBot(&botCfg, bm.globalConfig, bm.nickManager, bm)
	}

	// Dodawanie nowych botów do puli
	bm.mutex.Lock()
	originalCount := len(bm.bots)
	bm.bots = append(bm.bots, newBots...)
	bm.totalCreatedBots += len(newBots)
	bm.mutex.Unlock()

	// Aktualizacja NickManager
	bm.nickManager.SetBots(bm.bots)

	util.Info("Added %d new bots to the pool. Total bots: %d", len(newBots), originalCount+len(newBots))

	// Nie uruchamiamy botów automatycznie - będą uruchamiane przez StartBotsAndWait

	return len(newBots), nil
}

// StartBotsAndWait uruchamia boty asynchronicznie i czeka na ich połączenie
// Zwraca liczbę pomyślnie połączonych botów
func (bm *BotManager) StartBotsAndWait(bots []types.Bot) int {
	util.Info("Starting %d new bots asynchronously and waiting for connections", len(bots))

	// Licznik pomyślnie połączonych botów
	var connectedCount int32 = 0

	// Grupa wątków do synchronizacji
	var wg sync.WaitGroup

	// Mutex do bezpiecznego usuwania botów
	var removeMutex sync.Mutex

	// Maksymalna liczba równoczesnych połączeń - zwiększamy limit do 1000
	maxConcurrent := 1000
	if len(bots) < maxConcurrent {
		maxConcurrent = len(bots)
	}

	util.Info("Starting bots with max %d concurrent connections", maxConcurrent)

	// Semafor do ograniczenia liczby równoczesnych połączeń
	semaphore := make(chan struct{}, maxConcurrent)

	// Uruchamiamy połączenia botów asynchronicznie
	util.GoSafe(func() {
		for i, bot := range bots {
			wg.Add(1)

			go func(index int, b types.Bot) {
				defer wg.Done()

				// Używamy semafora do ograniczenia liczby równoczesnych połączeń
				semaphore <- struct{}{}
				defer func() {
					// Bezpieczne zwolnienie semafora - sprawdzamy czy kanał nie jest zamknięty
					select {
					case <-semaphore:
						// Kanał jest otwarty, zwolniliśmy slot
					default:
						// Kanał może być zamknięty lub pełny, ignorujemy
					}
				}()

				botNick := b.GetCurrentNick()
				util.Debug("[Bot %d/%d] Starting connection for %s", index+1, len(bots), botNick)

				// Maksymalnie 2 próby połączenia
				maxAttempts := 2
				connected := false

				for attempt := 1; attempt <= maxAttempts; attempt++ {
					// Sprawdzamy, czy kontekst nie został anulowany
					select {
					case <-bm.ctx.Done():
						util.Warning("[Bot %d/%d] Startup interrupted by context cancellation", index+1, len(bots))
						return
					default:
						// Kontynuuj
					}

					attemptStartTime := time.Now()
					util.Debug("[Bot %d/%d] Starting connection attempt %d/%d for %s",
						index+1, len(bots), attempt, maxAttempts, botNick)

					// Próba połączenia
					err := b.Connect()
					attemptDuration := time.Since(attemptStartTime)

					if err == nil {
						util.Info("[Bot %d/%d] %s connected successfully (attempt %d/%d) in %v",
							index+1, len(bots), botNick, attempt, maxAttempts, attemptDuration)
						atomic.AddInt32(&connectedCount, 1)
						connected = true
						break
					}

					util.Warning("[Bot %d/%d] Failed to connect %s: %v (attempt %d/%d) after %v",
						index+1, len(bots), botNick, err, attempt, maxAttempts, attemptDuration)

					// Jeśli to nie ostatnia próba, czekamy przed kolejną
					if attempt < maxAttempts {
						select {
						case <-bm.ctx.Done():
							return
						case <-time.After(time.Duration(bm.globalConfig.BotStartDelay) * time.Second):
							// Kontynuuj po opóźnieniu
						}
					}
				}

				// Jeśli bot nie został połączony po wszystkich próbach, usuwamy go
				if !connected {
					// Zwracamy słowa do puli przed usunięciem bota
					if concreteBot, ok := b.(*Bot); ok {
						if bm.wordPool != nil {
							concreteBot.returnWordsToPool()
						}
					}

					// Bezpieczne usuwanie bota
					removeMutex.Lock()
					bm.removeBot(b)
					removeMutex.Unlock()
				}
			}(i, bot)
		}
	})

	// Informujemy o oczekiwaniu na zakończenie połączeń
	util.Info("Waiting for all %d bot connections to complete (max 2 minutes)...", len(bots))
	startWait := time.Now()

	// Tworzymy kanał do obsługi timeoutu
	done := make(chan struct{})

	// Goroutine do obsługi WaitGroup
	util.GoSafe(func() {
		wg.Wait()
		close(done)
	})

	// Czekamy na zakończenie wszystkich połączeń lub timeout
	timeoutDuration := 2 * time.Minute
	timeout := time.After(timeoutDuration)

	select {
	case <-done:
		// Wszystkie połączenia zakończone przed timeoutem
		util.Info("All connection attempts completed before timeout")
	case <-timeout:
		// Upłynął timeout
		util.Warning("Timeout of %v reached. Some bots may still be connecting", timeoutDuration)
	}

	// NIE zamykamy semafora tutaj - gorutyny mogą jeszcze go używać
	// Semafor zostanie automatycznie zwolniony przez garbage collector

	waitDuration := time.Since(startWait)
	util.Info("Finished connecting bots in %v. Connected: %d/%d", waitDuration, connectedCount, len(bots))

	// Czyścimy niepołączone boty z tej partii
	var disconnectedBots []types.Bot
	for _, bot := range bots {
		if !bot.IsConnected() {
			disconnectedBots = append(disconnectedBots, bot)
		}
	}

	if len(disconnectedBots) > 0 {
		util.Info("Cleaning up %d disconnected bots from this batch", len(disconnectedBots))
		bm.cleanupSpecificBots(disconnectedBots)
	}

	return int(connectedCount)
}

// TrackDisconnect logs bot disconnections - library handles reconnection automatically
func (bm *BotManager) TrackDisconnect(bot types.Bot) {
	util.Info("Bot %s disconnected - library will handle reconnection automatically", bot.GetCurrentNick())
	// go-ircevo v1.2.0 handles reconnection automatically through Loop() method
	// No need for custom reconnection logic
}
