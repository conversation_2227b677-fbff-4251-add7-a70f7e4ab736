package bot

import (
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	irc "github.com/kofany/go-ircevo"
	"github.com/kofany/pNb/internal/auth"
	"github.com/kofany/pNb/internal/util"
)

type CommandType int

const (
	SingleCommand CommandType = iota
	MassCommand
)

type Command struct {
	Type    CommandType
	Handler func(*Bot, *irc.Event, []string)
}

var commandMap map[string]Command

func init() {
	commandMap = map[string]Command{
		"quit":       {Type: SingleCommand, Handler: handleQuitCommand},
		"say":        {Type: SingleCommand, Handler: handleSayCommand},
		"join":       {Type: MassCommand, Handler: handleJoinCommand},
		"part":       {Type: MassCommand, Handler: handlePartCommand},
		"reconnect":  {Type: MassCommand, Handler: handleReconnectCommand},
		"addnick":    {Type: SingleCommand, Handler: handleAddNickCommand},
		"delnick":    {Type: SingleCommand, Handler: handleDelNickCommand},
		"listnicks":  {Type: SingleCommand, Handler: handleListNicksCommand},
		"addowner":   {Type: SingleCommand, Handler: handleAddOwnerCommand},
		"delowner":   {Type: SingleCommand, Handler: handleDelOwnerCommand},
		"listowners": {Type: SingleCommand, Handler: handleListOwnersCommand},
		"cflo":       {Type: MassCommand, Handler: handleCfloodCommand},
		"nflo":       {Type: MassCommand, Handler: handleNfloodCommand},
	}
}

// handleTestCommand obsługuje komendę !test
func handleCfloodCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) < 3 {
		b.sendReply(isChannelMsg, target, sender, "Usage: !test <#channel> <loops> <message>")
		return
	}

	channel := args[0]
	loopsStr := args[1]
	message := strings.Join(args[2:], " ")

	loops, err := strconv.Atoi(loopsStr)
	if err != nil || loops <= 0 {
		b.sendReply(isChannelMsg, target, sender, "Ilość pętli musi być dodatnią liczbą całkowitą.")
		return
	}

	// Opcjonalne ograniczenie maksymalnej liczby pętli
	maxLoops := 100
	if loops > maxLoops {
		b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Maksymalna liczba pętli to %d.", maxLoops))
		return
	}

	b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Rozpoczynanie testu flood z %d pętlami.", loops))

	// Uruchomienie testu w osobnej gorutynie, aby nie blokować głównego wątku
	util.GoSafe(func() {
		for _, bot := range b.GetBotManager().GetBots() {
			// Asercja typu: zakładamy, że bot jest typu *Bot
			botInstance, ok := bot.(*Bot)
			if !ok {
				util.Debug("Failed to assert bot to *Bot")
				continue
			}
			go executeCflood(botInstance, channel, loops, message)
		}
	})
}

func handleNfloodCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) < 3 {
		b.sendReply(isChannelMsg, target, sender, "Usage: !test <#channel> <loops> <message>")
		return
	}

	targetNick := args[0]
	loopsStr := args[1]
	message := strings.Join(args[2:], " ")

	loops, err := strconv.Atoi(loopsStr)
	if err != nil || loops <= 0 {
		b.sendReply(isChannelMsg, target, sender, "Ilość pętli musi być dodatnią liczbą całkowitą.")
		return
	}

	// Opcjonalne ograniczenie maksymalnej liczby pętli
	maxLoops := 100
	if loops > maxLoops {
		b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Maksymalna liczba pętli to %d.", maxLoops))
		return
	}

	b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Rozpoczynanie testu flood z %d pętlami.", loops))

	// Uruchomienie testu w osobnej gorutynie, aby nie blokować głównego wątku
	util.GoSafe(func() {
		for _, bot := range b.GetBotManager().GetBots() {
			// Asercja typu: zakładamy, że bot jest typu *Bot
			botInstance, ok := bot.(*Bot)
			if !ok {
				util.Debug("Failed to assert bot to *Bot")
				continue
			}
			go executeNflood(botInstance, targetNick, loops, message)
		}
	})
}

func executeCflood(b *Bot, channel string, loops int, message string) {
	// Dołączanie do kanału
	joinCmd := fmt.Sprintf("JOIN %s", channel)
	b.SendRaw(joinCmd)
	time.Sleep(500 * time.Millisecond) // Krótkie opóźnienie, aby upewnić się, że bot dołączył

	for i := 0; i < loops; i++ {
		// Wysyłanie wiadomości
		privmsgCmd := fmt.Sprintf("PRIVMSG %s :%s", channel, message)
		b.SendRaw(privmsgCmd)

		// Czekanie 1 sekundę
		time.Sleep(1 * time.Second)

		// Zmiana nicku
		newNick := generateRandomNick()
		nickCmd := fmt.Sprintf("NICK %s", newNick)
		b.SendRaw(nickCmd)

		// Czekanie 2 sekundy
		time.Sleep(1 * time.Second)

		// Ponowne wysyłanie wiadomości
		b.SendRaw(privmsgCmd)

		// Czekanie 1 sekundę
		time.Sleep(1 * time.Second)

		// Ponowna zmiana nicku
		b.SendRaw(nickCmd)
	}
	partCmd := fmt.Sprintf("PART %s", channel)
	b.SendRaw(partCmd)
	time.Sleep(90 * time.Second)
	newNick := generateRandomNick()
	util.Info("==================>>> Zlecam Changenick na ------>>> %s", newNick)
	b.ChangeNick(newNick)
	time.Sleep(3 * time.Second)
}

func executeNflood(b *Bot, targetNick string, loops int, message string) {

	for i := 0; i < loops; i++ {
		newNick := generateRandomNick()
		nickCmd := fmt.Sprintf("NICK %s", newNick)
		b.SendRaw(nickCmd)
		// Wysyłanie wiadomości
		time.Sleep(1 * time.Second)

		privmsgCmd := fmt.Sprintf("PRIVMSG %s :%s", targetNick, message)
		b.SendRaw(privmsgCmd)

	}

	time.Sleep(10 * time.Second)
	newNick := generateRandomNick()
	b.ChangeNick(newNick)
}

func generateRandomNick() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	// Generowanie losowej długości głównej części nicka od 4 do 7 znaków
	mainLength := r.Intn(4) + 4
	mainPart := make([]byte, mainLength)

	// Generowanie głównej części nicka z zakresu Aa-Zz
	for i := range mainPart {
		if r.Intn(2) == 0 {
			mainPart[i] = byte('A' + r.Intn(26))
		} else {
			mainPart[i] = byte('a' + r.Intn(26))
		}
	}

	// Generowanie końcówki z zakresu Aa 0-9 Zz o długości od 1 do 4 znaków
	suffixLength := r.Intn(4) + 1
	suffixPart := make([]byte, suffixLength)

	for i := range suffixPart {
		choice := r.Intn(3)
		if choice == 0 {
			suffixPart[i] = byte('A' + r.Intn(26))
		} else if choice == 1 {
			suffixPart[i] = byte('a' + r.Intn(26))
		} else {
			suffixPart[i] = byte('0' + r.Intn(10))
		}
	}

	// Łączenie głównej części z końcówką i zwracanie jako string
	return fmt.Sprintf("%s%s", string(mainPart), string(suffixPart))
}

func (b *Bot) HandleCommands(e *irc.Event) {
	// If this is a drone bot, ignore commands
	if b.isDrone {
		return
	}

	util.Debug("Received command for bot %s: %s", b.GetCurrentNick(), e.Message())

	message := e.Message()
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")
	util.Debug("HandleCommands: isChannelMsg=%v, target=%s, sender=%s, message=%s", isChannelMsg, target, sender, message)

	if !startsWithAny(message, b.GlobalConfig.CommandPrefixes) {
		util.Debug("Message doesn't start with a command prefix: %s", message)
		return
	}

	if !auth.IsOwner(e, b.owners) {
		util.Debug("Command rejected: sender %s is not an owner", sender)
		return
	}

	commandLine := strings.TrimLeft(message, strings.Join(b.GlobalConfig.CommandPrefixes, ""))
	args := strings.Fields(commandLine)
	if len(args) == 0 {
		return
	}

	cmdName := strings.ToLower(args[0])
	cmd, exists := commandMap[cmdName]
	if !exists {
		util.Debug("Unknown command")
		return
	}

	util.Debug("Command %s recognized for bot %s", cmdName, b.GetCurrentNick())

	switch cmd.Type {
	case SingleCommand:
		util.Debug("Executing command %s for bot %s", cmdName, b.GetCurrentNick())
		cmd.Handler(b, e, args[1:])
	case MassCommand:
		if b.GetBotManager().CanExecuteMassCommand(cmdName) {
			util.Debug("Executing mass command %s for bot %s", cmdName, b.GetCurrentNick())
			cmd.Handler(b, e, args[1:])
		} else {
			util.Debug("Mass command %s not executed due to cooldown", cmdName)
		}
	}
}

func (b *Bot) sendReply(isChannelMsg bool, target, sender, message string) {
	if isChannelMsg {
		b.GetBotManager().CollectReactions(target, message, nil)
	} else {
		util.Debug("SendReply reciver: sender: %s, message: %s", sender, message)
		b.SendMessage(sender, message)
	}
}

func startsWithAny(s string, prefixes []string) bool {
	for _, prefix := range prefixes {
		if strings.HasPrefix(s, prefix) {
			return true
		}
	}
	return false
}

func handleQuitCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	b.sendReply(isChannelMsg, target, sender, "Quitting...")
	b.Quit("Command from owner")
}

func handleSayCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 2 {
		targetChannel := args[0]
		msg := strings.Join(args[1:], " ")
		if strings.HasPrefix(targetChannel, "#") {
			b.GetBotManager().CollectReactions(targetChannel, msg, nil)
		} else {
			b.SendMessage(targetChannel, msg)
		}
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: say <channel/nick> <message>")
	}
}

func handleJoinCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		channel := args[0]
		if isChannelMsg {
			b.GetBotManager().CollectReactions(target, fmt.Sprintf("All bots are joining channel %s", channel), func() error {
				for _, bot := range b.GetBotManager().GetBots() {
					bot.JoinChannel(channel)
				}
				return nil
			})
		} else {
			b.JoinChannel(channel)
			b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Joined channel %s", channel))
		}
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: join <channel>")
	}
}

func handlePartCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		channel := args[0]
		if isChannelMsg {
			b.GetBotManager().CollectReactions(target, fmt.Sprintf("All bots are leaving channel %s", channel), func() error {
				for _, bot := range b.GetBotManager().GetBots() {
					bot.PartChannel(channel)
				}
				return nil
			})
		} else {
			b.PartChannel(channel)
			b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Left channel %s", channel))
		}
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: part <channel>")
	}
}

func handleReconnectCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if isChannelMsg {
		b.GetBotManager().CollectReactions(target, "All bots are reconnecting with new nicks...", func() error {
			for _, bot := range b.GetBotManager().GetBots() {
				go bot.Reconnect()
			}
			return nil
		})
	} else {
		b.sendReply(isChannelMsg, target, sender, "Reconnecting with a new nick...")
		b.Reconnect()
	}
}

func handleAddNickCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		nick := args[0]
		b.GetBotManager().CollectReactions(
			target,
			fmt.Sprintf("Nick '%s' has been added.", nick),
			func() error { return b.GetNickManager().AddNick(nick) },
		)
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: addnick <nick>")
	}
}

func handleDelNickCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		nick := args[0]
		b.GetBotManager().CollectReactions(
			target,
			fmt.Sprintf("Nick '%s' has been removed.", nick),
			func() error { return b.GetNickManager().RemoveNick(nick) },
		)
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: delnick <nick>")
	}
}

func handleListNicksCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if isChannelMsg {
		b.GetBotManager().CollectReactions(
			target,
			"",
			func() error {
				nicks := b.GetNickManager().GetNicks()
				message := fmt.Sprintf("Current nicks: %s", strings.Join(nicks, ", "))
				b.GetBotManager().SendSingleMsg(target, message)
				return nil
			},
		)
	} else {
		nicks := b.GetNickManager().GetNicks()
		b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Current nicks: %s", strings.Join(nicks, ", ")))
	}
}

func handleAddOwnerCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		ownerMask := args[0]
		b.GetBotManager().CollectReactions(
			target,
			fmt.Sprintf("Owner '%s' has been added.", ownerMask),
			func() error { return b.GetBotManager().AddOwner(ownerMask) },
		)
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: addowner <mask>")
	}
}

func handleDelOwnerCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if len(args) >= 1 {
		ownerMask := args[0]
		b.GetBotManager().CollectReactions(
			target,
			fmt.Sprintf("Owner '%s' has been removed.", ownerMask),
			func() error { return b.GetBotManager().RemoveOwner(ownerMask) },
		)
	} else {
		b.sendReply(isChannelMsg, target, sender, "Usage: delowner <mask>")
	}
}

func handleListOwnersCommand(b *Bot, e *irc.Event, args []string) {
	sender := e.Nick
	target := e.Arguments[0]
	isChannelMsg := strings.HasPrefix(target, "#")

	if isChannelMsg {
		b.GetBotManager().CollectReactions(
			target,
			"",
			func() error {
				owners := b.GetBotManager().GetOwners()
				message := fmt.Sprintf("Current owners: %s", strings.Join(owners, ", "))
				b.GetBotManager().SendSingleMsg(target, message)
				return nil
			},
		)
	} else {
		owners := b.GetBotManager().GetOwners()
		b.sendReply(isChannelMsg, target, sender, fmt.Sprintf("Current owners: %s", strings.Join(owners, ", ")))
	}
}
