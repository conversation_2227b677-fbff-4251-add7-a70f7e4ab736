package bot

import (
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"
	"unicode"

	irc "github.com/kofany/go-ircevo"
	"github.com/kofany/pNb/internal/auth"
	"github.com/kofany/pNb/internal/config"
	"github.com/kofany/pNb/internal/dcc"
	"github.com/kofany/pNb/internal/types"
	"github.com/kofany/pNb/internal/util"
)

type Bot struct {
	Config          *config.BotConfig
	GlobalConfig    *config.GlobalConfig
	Connection      *irc.Connection
	CurrentNick     string
	PreviousNick    string // Store the previous nick for recovery during reconnection
	Username        string
	Realname        string
	isConnected     atomic.Bool
	owners          auth.OwnerList
	channels        []string
	nickManager     types.NickManager
	isReconnecting  bool
	lastConnectTime time.Time
	connected       chan struct{}
	botManager      types.BotManager
	gaveUp          bool
	isonResponse    chan []string
	ServerName      string
	mutex           sync.Mutex
	dccTunnel       *dcc.DCCTunnel
	IsMaster        bool
	isDrone         bool
}

func (b *Bot) GetBotManager() types.BotManager {
	return b.botManager
}

func (b *Bot) GetNickManager() types.NickManager {
	return b.nickManager
}

func (b *Bot) SetBotManager(manager types.BotManager) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	b.botManager = manager
}

func (b *Bot) SetNickManager(manager types.NickManager) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	b.nickManager = manager
}

func (b *Bot) GetMaxAttempts() int {
	return b.Config.MaxAttempts
}

func NewBot(cfg *config.BotConfig, globalConfig *config.GlobalConfig, nm types.NickManager, bm *BotManager) *Bot {
	var nick, ident, realname string

	if cfg.Nick != "" && cfg.Username != "" && cfg.RealName != "" {
		// Używamy nicków z konfiguracji (tryb proxy)
		nick = cfg.Nick
		ident = cfg.Username
		realname = cfg.RealName
		util.Debug("Using preconfigured names - Nick: %s, Ident: %s, Realname: %s", nick, ident, realname)
	} else {
		// Pobieramy słowa z nowej puli WordPool
		if bm.wordPool != nil {
			words, err := bm.wordPool.GetUniqueWords(3)
			if err != nil {
				util.Warning("Failed to get words from pool: %v, using fallback", err)
				nick = util.GenerateFallbackNick(bm.wordPool)
				ident = util.GenerateFallbackNick(bm.wordPool)
				realname = util.GenerateFallbackNick(bm.wordPool)
			} else {
				nick = words[0]
				ident = words[1]
				realname = words[2]
			}
		} else {
			nick = util.GenerateFallbackNick(bm.wordPool)
			ident = util.GenerateFallbackNick(bm.wordPool)
			realname = util.GenerateFallbackNick(bm.wordPool)
		}
		util.Debug("Using names from pool - Nick: %s, Ident: %s, Realname: %s", nick, ident, realname)
	}

	bot := &Bot{
		Config:       cfg,
		GlobalConfig: globalConfig,
		CurrentNick:  nick,
		Username:     ident,
		Realname:     realname,
		nickManager:  nm,
		botManager:   bm,
		isonResponse: make(chan []string, 1),
		isDrone:      true, // Default to drone mode
	}

	bot.isConnected.Store(false)
	bot.connected = make(chan struct{})

	nm.RegisterBot(bot)
	return bot
}

func (b *Bot) IsConnected() bool {
	// Podstawowe sprawdzenie połączenia
	if !b.isConnected.Load() || b.Connection == nil {
		return false
	}

	// Sprawdzenie stanu Connection - polegamy tylko na bibliotece
	if !b.Connection.IsFullyConnected() {
		util.Debug("Bot %s connection reports not fully connected", b.CurrentNick)
		return false
	}

	// Jeśli biblioteka raportuje, że bot jest połączony, to uznajemy go za połączony
	// Całkowicie usunięto zależność od ping
	return true
}

// Nowa funkcja do zwracania słów do puli
func (b *Bot) returnWordsToPool() {
	if b == nil {
		util.Warning("Cannot return words to pool: bot is nil")
		return
	}

	b.mutex.Lock()
	defer b.mutex.Unlock()

	// Sprawdzamy, czy botManager i wordPool są dostępne
	if b.botManager == nil {
		util.Debug("Cannot return words to pool: botManager is nil for bot %s", b.CurrentNick)
		return
	}

	bm, ok := b.botManager.(*BotManager)
	if !ok {
		util.Debug("Cannot return words to pool: botManager is not of type *BotManager for bot %s", b.CurrentNick)
		return
	}

	if bm.wordPool == nil {
		util.Debug("Cannot return words to pool: wordPool is nil for bot %s", b.CurrentNick)
		return
	}

	// Tylko jeśli słowa nie są puste, zwracamy je do puli
	if b.CurrentNick != "" {
		bm.wordPool.ReturnWordToPool(b.CurrentNick)
		util.Debug("Returned nick %s to pool", b.CurrentNick)
	}

	if b.Username != "" {
		bm.wordPool.ReturnWordToPool(b.Username)
		util.Debug("Returned username %s to pool", b.Username)
	}

	if b.Realname != "" {
		bm.wordPool.ReturnWordToPool(b.Realname)
		util.Debug("Returned realname %s to pool", b.Realname)
	}

	util.Debug("Words successfully returned to pool for bot %s", b.CurrentNick)
}

// getNewWordsFromPool pobiera nowe słowa z puli i obsługuje błędy
func (b *Bot) getNewWordsFromPool() (nick, username, realname string) {
	if b.botManager == nil {
		util.Warning("Cannot get new words: botManager is nil")
		return generateFallbackWords()
	}

	bm, ok := b.botManager.(*BotManager)
	if !ok || bm.wordPool == nil {
		util.Warning("Cannot get new words: invalid botManager or wordPool is nil")
		return generateFallbackWords()
	}

	words, err := bm.wordPool.GetUniqueWords(3)
	if err != nil {
		util.Warning("Failed to get new words from pool: %v", err)
		return generateFallbackWords()
	}

	if len(words) < 3 {
		util.Warning("Received fewer words than needed: got %d, need 3", len(words))
		return generateFallbackWords()
	}

	return words[0], words[1], words[2]
}

// generateFallbackWords generuje fallbackowe słowa bez puli
func generateFallbackWords() (nick, username, realname string) {
	return util.GenerateFallbackNick(nil),
		util.GenerateFallbackNick(nil),
		util.GenerateFallbackNick(nil)
}

// Dodanie typów dla kategoryzacji błędów
type ConnectionErrorType int

const (
	TemporaryError ConnectionErrorType = iota
	PermanentError
	NetworkError
	ServerError
)

// String implementuje interfejs Stringer dla ConnectionErrorType
func (e ConnectionErrorType) String() string {
	switch e {
	case TemporaryError:
		return "TemporaryError"
	case PermanentError:
		return "PermanentError"
	case NetworkError:
		return "NetworkError"
	case ServerError:
		return "ServerError"
	default:
		return "UnknownError"
	}
}

// Funkcja do kategoryzacji błędów
func categorizeConnectionError(errorMsg string) ConnectionErrorType {
	// Permanent errors - kończące działanie bota
	if strings.Contains(errorMsg, "K-lined") ||
		strings.Contains(errorMsg, "banned") ||
		strings.Contains(errorMsg, "G-lined") {
		return PermanentError
	}

	// Server errors - błędy po stronie serwera
	if strings.Contains(errorMsg, "Too many host connections") ||
		strings.Contains(errorMsg, "too many") ||
		strings.Contains(errorMsg, "connections") ||
		strings.Contains(errorMsg, "Closing Link") {
		return ServerError
	}

	// Network errors - błędy sieci
	if strings.Contains(errorMsg, "connection refused") ||
		strings.Contains(errorMsg, "timeout") ||
		strings.Contains(errorMsg, "no route to host") ||
		strings.Contains(errorMsg, "network is unreachable") {
		return NetworkError
	}

	// Default - inne tymczasowe błędy
	return TemporaryError
}

// Connect establishes a connection to the IRC server with retry logic
func (b *Bot) Connect() error {
	util.Info("[DEBUG] Connect() start for bot %s (nick: %s, user: %s, real: %s, proxy: %+v, vhost: %s, server: %s)", b.CurrentNick, b.Username, b.Realname, b.Config.Proxy, b.Config.Vhost, b.Config.Server)
	b.isConnected.Store(false)

	b.mutex.Lock()
	if b.connected != nil {
		select {
		case <-b.connected:
		default:
			close(b.connected)
		}
	}
	b.connected = make(chan struct{})
	b.mutex.Unlock()
	util.Debug("Connection channel created for bot %s", b.CurrentNick)

	// Dodanie obsługi timeoutów połączenia
	connectionTimeout := time.Duration(b.GlobalConfig.ConnectionTimeout) * time.Second
	if connectionTimeout == 0 {
		connectionTimeout = 30 * time.Second // domyślny timeout
	}

	connectChan := make(chan error, 1)
	go func() {
		connectChan <- b.connectWithRetry(b.Config.Proxy != nil)
	}()

	select {
	case err := <-connectChan:
		if err != nil {
			util.Info("[DEBUG] Connect() failed for bot %s: %v", b.CurrentNick, err)
			b.returnWordsToPool()
			return fmt.Errorf("connection failed: %v", err)
		}
		util.Info("[DEBUG] Connect() success for bot %s", b.CurrentNick)
		return nil
	case <-time.After(connectionTimeout):
		util.Info("[DEBUG] Connect() timeout for bot %s", b.CurrentNick)
		if b.Connection != nil {
			b.Connection.Quit()
		}
		b.returnWordsToPool()
		return fmt.Errorf("connection timeout after %v", connectionTimeout)
	}
}

// Połączona funkcja łącząca poprzednie connectWithRetryProxy i connectWithRetry
func (b *Bot) connectWithRetry(useProxy bool) error {
	maxRetries := b.GlobalConfig.ReconnectRetries
	if useProxy {
		maxRetries = b.Config.MaxAttempts
	}
	retryInterval := time.Duration(b.GlobalConfig.ReconnectInterval) * time.Second

	util.Info("[DEBUG] connectWithRetry() for bot %s, useProxy=%v, maxRetries=%d, retryInterval=%v", b.CurrentNick, useProxy, maxRetries, retryInterval)

	for attempts := 0; attempts < maxRetries; attempts++ {
		b.mutex.Lock()
		if b.gaveUp {
			b.mutex.Unlock()
			util.Info("[DEBUG] connectWithRetry(): bot %s gave up before attempt %d", b.CurrentNick, attempts+1)
			return fmt.Errorf("bot has given up")
		}
		// Nie tworzymy nowego kanału w każdej iteracji - używamy istniejącego
		b.mutex.Unlock()

		b.Connection = irc.IRC(b.CurrentNick, b.Username)
		b.Connection.VerboseCallbackHandler = false
		b.Connection.Debug = false
		b.Connection.UseTLS = b.Config.SSL
		b.Connection.RealName = b.Realname

		if useProxy && b.Config.Proxy != nil {
			util.Info("[DEBUG] Setting proxy for bot %s: %+v", b.CurrentNick, b.Config.Proxy)
			b.Connection.SetProxy(
				b.Config.Proxy.Type,
				b.Config.Proxy.Address,
				b.Config.Proxy.Username,
				b.Config.Proxy.Password,
			)
		} else {
			util.Info("[DEBUG] Setting vhost for bot %s: %s", b.CurrentNick, b.Config.Vhost)
			b.Connection.SetLocalIP(b.Config.Vhost)
		}

		b.addCallbacks()

		util.Info("[DEBUG] Attempting to connect bot %s to %s (attempt %d/%d)",
			b.CurrentNick, b.Config.ServerAddress(), attempts+1, maxRetries)

		err := b.Connection.Connect(b.Config.ServerAddress())
		if err != nil {
			util.Error("[DEBUG] Attempt %d: Failed to connect bot %s: %v", attempts+1, b.CurrentNick, err)

			// Sprawdzamy czy to błąd proxy czy serwera
			if useProxy && strings.Contains(err.Error(), "proxy") {
				util.Error("[DEBUG] Proxy connection failed for bot %s: %v", b.CurrentNick, err)
			}

			if attempts < maxRetries-1 {
				util.Debug("[DEBUG] Waiting %v before retry for bot %s", retryInterval, b.CurrentNick)
				time.Sleep(retryInterval)
			}
			continue
		}

		errorChan := make(chan struct{})
		util.GoSafe(func() {
			b.Connection.Loop()
			close(errorChan)
		})

		b.mutex.Lock()
		waitChan := b.connected
		b.mutex.Unlock()

		if waitChan == nil {
			if b.IsConnected() {
				util.Info("[DEBUG] Connection already established for bot %s", b.CurrentNick)
				return nil
			}
			util.Warning("[DEBUG] Connection channel is nil for bot %s", b.CurrentNick)
			return fmt.Errorf("connection interrupted")
		}

		// Zwiększamy timeout dla proxy connections
		connectionWaitTimeout := 60 * time.Second
		if useProxy {
			connectionWaitTimeout = 90 * time.Second // Dłuższy timeout dla proxy
		}

		select {
		case <-waitChan:
			if b.gaveUp {
				util.Info("[DEBUG] connectWithRetry(): bot %s gave up during connection", b.CurrentNick)
				return fmt.Errorf("bot has given up during connection")
			}
			if b.IsConnected() {
				util.Info("[DEBUG] Bot %s successfully connected", b.CurrentNick)
				return nil
			}
			util.Warning("[DEBUG] Bot %s received connection signal but is not connected", b.CurrentNick)
		case <-errorChan:
			util.Info("[DEBUG] IRC loop ended for bot %s", b.CurrentNick)
			return fmt.Errorf("connection loop ended")
		case <-time.After(connectionWaitTimeout):
			if b.gaveUp {
				util.Info("[DEBUG] connectWithRetry(): bot %s gave up during timeout", b.CurrentNick)
				return fmt.Errorf("bot has given up during timeout")
			}
			if b.IsConnected() {
				util.Info("[DEBUG] Bot %s is fully connected, proceeding", b.CurrentNick)
				return nil
			}
			util.Warning("[DEBUG] Bot %s connection timeout after %v, will retry", b.CurrentNick, connectionWaitTimeout)
			if b.Connection != nil {
				b.Connection.Quit()
			}
		}

		if attempts < maxRetries-1 {
			util.Debug("[DEBUG] Waiting %v before next retry for bot %s", retryInterval, b.CurrentNick)
			time.Sleep(retryInterval)
		}
	}

	b.mutex.Lock()
	b.gaveUp = true
	b.mutex.Unlock()
	util.Info("[DEBUG] connectWithRetry(): bot %s could not connect after %d attempts", b.CurrentNick, maxRetries)
	return fmt.Errorf("bot %s could not connect after %d attempts", b.CurrentNick, maxRetries)
}

func (b *Bot) updateNickInfo(newNick string) {
	b.mutex.Lock()
	oldNick := b.CurrentNick
	actualNick := b.Connection.GetNick()

	// Sprawdzamy czy przekazany nick zgadza się z aktualnym nickiem z biblioteki
	if actualNick != newNick {
		util.Warning("Nick mismatch - expected: %s, actual: %s", newNick, actualNick)
		// Możemy też zalogować zdarzenie do debugowania
		util.Debug("Nick update discrepancy - passed: %s, irc.current: %s",
			newNick, actualNick)
	}

	b.CurrentNick = actualNick
	b.mutex.Unlock()

	if oldNick != actualNick {
		util.Debug("Bot nick changed from %s to %s", oldNick, actualNick)
	}
}

// Refaktoryzacja addCallbacks do bardziej logicznych grup
func (b *Bot) addCallbacks() {
	// Najpierw sprawdź konflikt flag IsMaster i isDrone
	if b.IsMaster && b.isDrone {
		util.Warning("Inconsistent bot flags detected: IsMaster=true and isDrone=true - fixing...")
		b.isDrone = false
		util.Info("Fixed flags for MasterBot %s: isDrone=false", b.CurrentNick)
	}

	// 1. Callbacks związane z połączeniem
	b.addConnectionCallbacks()

	// 2. Callbacks związane z monitorowaniem połączenia
	b.addMonitoringCallbacks()

	// 3. Callbacks związane z obsługą wiadomości
	b.addMessageCallbacks()

	// 4. Callbacks specyficzne dla MasterBota
	if !b.isDrone {
		b.addMasterBotCallbacks()
	}

	// 5. Różne/inne callbacks
	b.addMiscCallbacks()
}

// Zdefiniowane metody do obsługi grup callbacków
func (b *Bot) addConnectionCallbacks() {
	// Obsługa ERROR
	b.Connection.AddCallback("ERROR", func(e *irc.Event) {
		currentNick := b.Connection.GetNick()
		errorMessage := e.Message()
		errorType := categorizeConnectionError(errorMessage)

		util.Warning("Bot %s received ERROR (%s): %s",
			currentNick, errorType, errorMessage)

		b.mutex.Lock()
		if b.gaveUp {
			b.mutex.Unlock()
			return
		}
		b.mutex.Unlock()

		b.isConnected.Store(false)

		switch errorType {
		case PermanentError:
			util.Error("Bot %s received permanent ERROR, removing bot", currentNick)
			b.mutex.Lock()
			b.gaveUp = true
			b.mutex.Unlock()

			if b.Connection != nil {
				b.Connection.Quit()
			}

			b.returnWordsToPool()

			if b.botManager != nil {
				bm := b.botManager.(*BotManager)
				bm.removeBot(b)
			}
			return

		case ServerError:
			util.Warning("Bot %s received server ERROR, waiting before reconnect", currentNick)
			// Zwiększony delay dla błędów serwera
			delay := time.Duration(b.GlobalConfig.MaxReconnectDelay) * time.Second
			time.Sleep(delay)
			go b.handleReconnectInternal()

		case NetworkError:
			util.Warning("Bot %s received network ERROR, attempting quicker reconnect", currentNick)
			// Krótszy delay dla błędów sieci
			delay := time.Duration(b.GlobalConfig.MinReconnectDelay) * time.Second
			time.Sleep(delay)
			go b.handleReconnectInternal()

		default:
			// Zwykły reconnect dla pozostałych typów błędów
			go b.handleReconnectInternal()
		}
	})

	// Obsługa pomyślnego połączenia (001)
	b.Connection.AddCallback("001", func(e *irc.Event) {
		if b.isConnected.Load() {
			return
		}

		if len(e.Arguments) == 0 {
			util.Error("Bot %s received invalid 001 message (no arguments)", b.Connection.GetNick())
			return
		}

		// Używamy GetNick() jako źródła prawdy
		actualNick := b.Connection.GetNick()

		b.updateNickInfo(actualNick)
		b.isConnected.Store(true)
		b.ServerName = e.Source
		b.lastConnectTime = time.Now()

		util.Info("Bot %s fully connected to %s as %s (verified)",
			actualNick, b.ServerName, actualNick)

		// Jeśli to MasterBot, dołącz do skonfigurowanych kanałów
		if b.IsMaster {
			util.Info("MasterBot %s is joining configured channels", actualNick)
			for _, channel := range b.channels {
				util.Info("MasterBot joining channel: %s", channel)
				b.Connection.Join(channel)
			}
		} else {
			// Dla zwykłych botów dołącz tylko do #rr
			util.Debug("Bot %s joining #rr channel", actualNick)
			b.Connection.Join("#rr")
		}

		// Upewniamy się, że bot jest oznaczony jako w pełni połączony
		b.isConnected.Store(true)

		// Dodajemy bota do listy aktywnych botów w BotManager
		if b.botManager != nil {
			if bm, ok := b.botManager.(*BotManager); ok {
				bm.MarkBotAsActive(b)
				util.Debug("Bot %s marked as active in BotManager", actualNick)
			}
		}

		b.mutex.Lock()
		if b.connected != nil {
			close(b.connected)
			b.connected = nil
		}
		b.mutex.Unlock()
	})

	// Obsługa DISCONNECTED
	b.Connection.AddCallback("DISCONNECTED", func(e *irc.Event) {
		currentNick := b.Connection.GetNick()
		util.Warning("Bot %s disconnected from server %s", currentNick, b.ServerName)

		wasConnected := b.isConnected.Swap(false)

		if wasConnected {
			// Powiadom BotManager o rozłączeniu, aby mógł wykryć masowe rozłączenia
			if b.botManager != nil {
				if bm, ok := b.botManager.(*BotManager); ok {
					// Użyj TrackDisconnect do obsługi rozłączenia
					util.Debug("Notifying BotManager about disconnect of bot %s", currentNick)
					bm.TrackDisconnect(b)
					return // TrackDisconnect zajmie się reconnectem
				}
			}

			// Jeśli nie ma BotManagera lub nie udało się rzutować, użyj standardowej procedury
			b.returnWordsToPool()
			go b.handleReconnectInternal()
		} else {
			util.Info("Bot %s was already disconnected from %s", currentNick, b.ServerName)
		}
	})
}

func (b *Bot) addMonitoringCallbacks() {
	// Obsługa PING
	b.Connection.AddCallback("PING", func(e *irc.Event) {
		b.updatePingStatus()
	})

	// Obsługa PONG
	b.Connection.AddCallback("PONG", func(e *irc.Event) {
		b.updatePingStatus()
	})

	// Obsługa dołączenia do kanału (JOIN)
	b.Connection.AddCallback("JOIN", func(e *irc.Event) {
		// Sprawdzamy, czy to nasz bot dołączył do kanału
		if e.Nick == b.Connection.GetNick() {
			channel := e.Arguments[0]
			util.Debug("Bot %s joined channel %s", b.CurrentNick, channel)

			// Jeśli bot dołączył do kanału #rr, upewniamy się, że jest oznaczony jako aktywny
			if channel == "#rr" {
				// Upewniamy się, że bot jest oznaczony jako w pełni połączony
				b.isConnected.Store(true)

				// Dodajemy bota do listy aktywnych botów w BotManager
				if b.botManager != nil {
					if bm, ok := b.botManager.(*BotManager); ok {
						bm.MarkBotAsActive(b)
						util.Debug("Bot %s marked as active in BotManager after joining #rr", b.CurrentNick)
					}
				}
			}
		}
	})

	// Obsługa zmiany nicka (NICK)
	b.Connection.AddCallback("NICK", func(e *irc.Event) {
		oldNick := e.Nick
		newNick := e.Message()

		// Check if this is our own nick change
		if e.Nick == b.Connection.GetNick() || (b.PreviousNick != "" && e.Nick == b.PreviousNick) {
			// Update PreviousNick for potential recovery
			b.PreviousNick = newNick
			util.Info("Bot nick changed from %s to %s", oldNick, newNick)

			// Check for single-letter nick changes
			wasOneLetter := len(oldNick) == 1
			isOneLetter := len(newNick) == 1

			// Bot got a single letter nick
			if !wasOneLetter && isOneLetter {
				util.Debug("Bot got single letter nick, joining #literki")
				b.JoinChannel("#literki")
			}

			// Bot lost a single letter nick
			if wasOneLetter && !isOneLetter {
				util.Debug("Bot lost single letter nick, leaving #literki")
				b.PartChannel("#literki")
			}

			// Update nick info
			b.updateNickInfo(newNick)
		}
	})

	// Obsługa zakończenia połączenia (376)
	b.Connection.AddCallback("376", func(e *irc.Event) {
		actualNick := b.Connection.GetNick()
		oldNick := b.CurrentNick

		if oldNick != actualNick {
			if bm, ok := b.botManager.(*BotManager); ok && bm.wordPool != nil {
				bm.wordPool.ReturnWordToPool(oldNick)
				bm.wordPool.MarkWordAsUsed(actualNick)
			}
			b.updateNickInfo(actualNick)
			util.Debug("Bot %s successfully updated nick to %s", oldNick, actualNick)
		}
	})

	// Obsługa błędu nicka (432)
	b.Connection.AddCallback("432", func(e *irc.Event) {
		currentNick := b.Connection.GetNick()
		util.Warning("Bot %s encountered error 432: %s", currentNick, e.Message())
		if len(e.Arguments) > 1 {
			nickInQuestion := e.Arguments[1]
			if len(nickInQuestion) == 1 {
				util.Warning("Server %s does not accept single-letter nick %s. Marking it.",
					b.ServerName, nickInQuestion)
				if b.nickManager != nil {
					b.nickManager.MarkServerNoLetters(b.ServerName)
				}
			} else if b.nickManager != nil {
				b.nickManager.MarkNickAsTemporarilyUnavailable(nickInQuestion)
			}

			if bm, ok := b.botManager.(*BotManager); ok && bm.wordPool != nil {
				bm.wordPool.ReturnWordToPool(nickInQuestion)
			}
		}
	})

	// Obsługa błędu nicka (433) - nick już w użyciu
	b.Connection.AddCallback("433", func(e *irc.Event) {
		currentNick := b.Connection.GetNick()
		util.Warning("Bot %s encountered error 433 (nick in use): %s", currentNick, e.Message())
		if len(e.Arguments) > 1 {
			nickInQuestion := e.Arguments[1]

			// Zwróć nick do puli
			if bm, ok := b.botManager.(*BotManager); ok && bm.wordPool != nil {
				bm.wordPool.ReturnWordToPool(nickInQuestion)
			}

			// Jeśli to nasz aktualny nick, spróbuj wygenerować nowy
			if nickInQuestion == b.CurrentNick {
				newNick, _, _ := b.getNewWordsFromPool()
				util.Info("Nick %s already in use, trying with %s", nickInQuestion, newNick)
				b.Connection.Nick(newNick)
			}
		}
	})
}

func (b *Bot) addMessageCallbacks() {
	// Obsługa PRIVMSG
	b.Connection.AddCallback("PRIVMSG", func(e *irc.Event) {
		// Skip if this is a DCC chat request (handled separately for MasterBot)
		if strings.Contains(e.Message(), "DCC CHAT") {
			return
		}

		// For drone bots, only forward to tunnel if exists
		if b.isDrone && b.dccTunnel != nil {
			b.ForwardToTunnel(e.Raw)
			return
		}

		// For MasterBot, process the message and check for commands
		if !b.isDrone {
			// Handle regular chat messages
			b.handlePrivMsg(e)
		}
	})

	// Obsługa INVITE
	b.Connection.AddCallback("INVITE", b.handleInvite)

	// Obsługa wszystkich wiadomości (*)
	b.Connection.AddCallback("*", func(e *irc.Event) {
		if b.dccTunnel != nil {
			b.dccTunnel.WriteToConn(e.Raw)
		}
		b.ForwardToTunnel(e.Raw)
	})
}

func (b *Bot) addMasterBotCallbacks() {
	// Only add these callbacks for MasterBot, not for drone bots
	util.Info("Adding special DCC and CTCP handlers for MasterBot %s", b.CurrentNick)

	// Add DCC chat callback
	b.Connection.AddCallback("PRIVMSG", func(e *irc.Event) {
		if strings.Contains(e.Message(), "DCC CHAT") {
			b.handleDCCRequest(e)
		}
	})

	// Add CTCP callback
	b.Connection.AddCallback("CTCP", func(e *irc.Event) {
		b.handleCTCP(e)
	})

	// Configure master DCC if this is the MasterBot
	if b.IsMaster {
		b.ConfigureMasterDCCHandler()
	}

	b.Connection.AddCallback("CTCP_VERSION", func(e *irc.Event) {
		response := "\x01VERSION WeeChat 4.4.2\x01"
		b.Connection.SendRawf("NOTICE %s :%s", e.Nick, response)
	})

	b.Connection.AddCallback("CTCP", b.handleCTCP)
	b.Connection.AddCallback("CTCP_DCC", b.handleDCCRequest)
	b.Connection.AddCallback("CTCP_*", func(e *irc.Event) {
		util.Debug("DCC: CTCP Event Code: %s | Nick: %s | Args: %v | Message: %s",
			e.Code, e.Nick, e.Arguments, e.Message())
	})
}

func (b *Bot) addMiscCallbacks() {
	// Start a goroutine to periodically send PINGs if server doesn't ping us
	go b.startPingChecker()
}

func (b *Bot) GetServerName() string {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	return b.ServerName
}

// Ulepszony handleReconnectInternal wykorzystujący nowy system zarządzania słowami
func (b *Bot) handleReconnectInternal() {
	b.mutex.Lock()
	if b.gaveUp {
		b.mutex.Unlock()
		return
	}
	b.isReconnecting = true
	b.mutex.Unlock()

	defer func() {
		b.mutex.Lock()
		b.isReconnecting = false
		b.mutex.Unlock()
	}()

	// Sprawdzenie czasu od ostatniego połączenia
	now := time.Now()
	minReconnectTime := time.Duration(b.GlobalConfig.MinReconnectDelay) * time.Second
	if minReconnectTime == 0 {
		minReconnectTime = 10 * time.Second // domyślna wartość
	}

	if now.Sub(b.lastConnectTime) < minReconnectTime {
		util.Debug("Too many reconnect attempts in short time, waiting...")
		time.Sleep(minReconnectTime)
	}

	// Zwrot starych słów do puli
	b.returnWordsToPool()

	// Pobranie nowych słów do połączenia
	b.mutex.Lock()
	b.CurrentNick, b.Username, b.Realname = b.getNewWordsFromPool()
	b.mutex.Unlock()

	util.Info("Reconnecting bot with new identifiers: nick=%s, username=%s",
		b.CurrentNick, b.Username)

	// Konfiguracja exponential backoff
	maxRetries := b.Config.MaxAttempts
	backoff := util.ExponentialBackoff{
		Initial:  time.Duration(b.GlobalConfig.MinReconnectDelay) * time.Second,
		Max:      time.Duration(b.GlobalConfig.MaxReconnectDelay) * time.Second,
		Factor:   2.0,
		MaxCount: maxRetries,
	}

	// Próby reconnectu z exponential backoff
	for attempts := 0; attempts < maxRetries; attempts++ {
		util.Info("Bot %s is attempting to reconnect (attempt %d/%d)",
			b.CurrentNick, attempts+1, maxRetries)

		b.mutex.Lock()
		if b.gaveUp {
			b.mutex.Unlock()
			return
		}
		b.mutex.Unlock()

		// Próba połączenia
		err := b.connectWithRetry(b.Config.Proxy != nil)
		if err == nil {
			b.lastConnectTime = time.Now()
			util.Info("Bot %s reconnected successfully with new identifiers", b.CurrentNick)
			return
		}

		util.Error("Reconnect attempt %d failed: %v", attempts+1, err)

		if attempts == maxRetries-1 {
			b.mutex.Lock()
			b.gaveUp = true
			b.mutex.Unlock()

			if b.botManager != nil {
				bm := b.botManager.(*BotManager)
				bm.removeBot(b)
			}
			return
		}

		// Wykładniczy backoff
		delay := backoff.NextDelay()
		util.Debug("Waiting %v before next reconnect attempt", delay)
		time.Sleep(delay)
	}

	util.Error("Bot %s could not reconnect after %d attempts", b.CurrentNick, maxRetries)
	b.mutex.Lock()
	b.gaveUp = true
	b.mutex.Unlock()
}

func (b *Bot) handleInvite(e *irc.Event) {
	inviter := e.Nick
	channel := e.Arguments[1]

	if auth.IsOwner(e, b.owners) {
		util.Info("Bot %s received INVITE to %s from owner %s", b.CurrentNick, channel, inviter)
		b.JoinChannel(channel)
	} else {
		util.Debug("Bot %s ignored INVITE to %s from non-owner %s", b.CurrentNick, channel, inviter)
	}
}

func (b *Bot) RequestISON(nicks []string) ([]string, error) {
	// ISON functionality has been removed
	util.Debug("ISON functionality has been removed, ignoring RequestISON call")
	return []string{}, nil
}

func (b *Bot) Quit(message string) {
	if b.IsConnected() {
		util.Info("Bot %s is disconnecting: %s", b.CurrentNick, message)

		// Zwróć słowa do puli przed rozłączeniem
		b.returnWordsToPool()

		b.Connection.QuitMessage = message
		b.Connection.Quit()
		b.isConnected.Store(false)

		if b.dccTunnel != nil {
			b.dccTunnel.Stop()
			b.dccTunnel = nil
		}

		// Dodajemy mutex i sprawdzenie nil
		b.mutex.Lock()
		if b.connected != nil {
			select {
			case <-b.connected: // Sprawdź czy kanał nie jest już zamknięty
			default:
				close(b.connected)
			}
			b.connected = nil
		}
		b.mutex.Unlock()
	}
}

func shuffleNick(nick string) string {
	runes := []rune(nick)
	rand.Shuffle(len(runes), func(i, j int) {
		runes[i], runes[j] = runes[j], runes[i]
	})

	if unicode.IsDigit(runes[0]) {
		return "a_" + string(runes)
	}

	return string(runes)
}

func (b *Bot) SendRaw(message string) {
	if b.IsConnected() {
		b.Connection.SendRaw(message)
	}
}

func (b *Bot) ForwardToTunnel(data string) {
	if b.dccTunnel != nil {
		b.dccTunnel.WriteToConn(data)
	}
}

// Centralizacja obsługi DCC dla master i drone botów
func (b *Bot) handleDCCRequest(e *irc.Event) {
	// Jeśli to jest bot drone, ignoruj żądania DCC
	if b.isDrone {
		util.Debug("Ignoring DCC request for drone bot %s", b.CurrentNick)
		return
	}

	util.Debug("DCC: handleDCCRequest called with Event Code: %s | Nick: %s | Args: %v | Message: %s",
		e.Code, e.Nick, e.Arguments, e.Message())

	// Wspólna logika parsowania DCC
	ctcpMessage := e.Message()
	dccArgs := strings.Fields(ctcpMessage)

	// Wczesne sprawdzenie czy to jest żądanie DCC CHAT
	if len(dccArgs) < 4 || strings.ToUpper(dccArgs[0]) != "DCC" || strings.ToUpper(dccArgs[1]) != "CHAT" {
		dccCommand := "unknown"
		if len(dccArgs) > 1 {
			dccCommand = dccArgs[1]
		}
		util.Debug("DCC: Not a DCC CHAT request from %s. DCC Command: %s", e.Nick, dccCommand)
		return
	}

	// Weryfikacja właściciela
	if !auth.IsOwner(e, b.owners) {
		util.Debug("DCC: Ignoring DCC CHAT request from non-owner %s", e.Nick)
		return
	}

	// Parsowanie parametrów adresu DCC
	ipStr, portStr, err := b.parseDCCAddressParams(dccArgs)
	if err != nil {
		util.Warning("DCC: Invalid address parameters: %v", err)
		return
	}

	// Nawiązanie połączenia
	conn, err := b.connectToDCC(ipStr, portStr)
	if err != nil {
		util.Error("DCC: Failed to connect: %v", err)
		return
	}

	// Utworzenie odpowiedniego typu tunelu
	b.createAndStartDCCTunnel(conn, e.Nick)
}

// Pomocnicze funkcje DCC
func (b *Bot) parseDCCAddressParams(dccArgs []string) (string, string, error) {
	argIndex := 2
	if strings.ToLower(dccArgs[argIndex]) == "chat" {
		argIndex++
	}

	if len(dccArgs) <= argIndex+1 {
		return "", "", fmt.Errorf("not enough arguments for DCC CHAT request")
	}

	ipStr := dccArgs[argIndex]
	portStr := dccArgs[argIndex+1]

	// Sprawdź, czy adres IP jest liczbą (dla IPv4)
	ipNum, err := strconv.ParseUint(ipStr, 10, 64)
	if err == nil {
		// Adres IP jest liczbą - konwertuj na IPv4
		ip := intToIP(uint32(ipNum))
		ipStr = ip.String()
		util.Debug("DCC: Converted numeric IP %s to dotted format: %s", dccArgs[argIndex], ipStr)
	} else {
		// Adres IP nie jest liczbą - załóż, że to adres tekstowy (IPv6 lub IPv4)
		util.Debug("DCC: IP address is not numeric, using textual IP: %s", ipStr)
		parsedIP := net.ParseIP(ipStr)
		if parsedIP == nil {
			return "", "", fmt.Errorf("invalid IP address: %s", ipStr)
		}
	}

	return ipStr, portStr, nil
}

func (b *Bot) connectToDCC(ipStr, portStr string) (net.Conn, error) {
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return nil, fmt.Errorf("invalid port: %v", err)
	}

	addr := net.JoinHostPort(ipStr, strconv.Itoa(port))
	util.Debug("DCC: Connecting to %s for DCC CHAT", addr)

	// Wybierz odpowiedni protokół (tcp4 lub tcp6)
	var network string
	if strings.Contains(ipStr, ":") {
		network = "tcp6"
	} else {
		network = "tcp4"
	}

	conn, err := net.Dial(network, addr)
	if err != nil {
		return nil, fmt.Errorf("failed to connect: %v", err)
	}

	return conn, nil
}

func (b *Bot) createAndStartDCCTunnel(conn net.Conn, ownerNick string) {
	// Get reference to DCC tunnel factory
	factory := dcc.GetTunnelFactory()

	// Create appropriate tunnel based on bot type
	if b.IsMaster {
		util.Info("Creating MasterDCCTunnel for MasterBot %s", b.CurrentNick)
		masterTunnel := factory.CreateMasterDCCTunnel(b, ownerNick, b.botManager)

		// Start the tunnel
		if tunnel, ok := masterTunnel.(*dcc.MasterDCCTunnel); ok {
			// Store the parent DCCTunnel in the bot for future reference
			b.dccTunnel = tunnel.DCCTunnel
			util.Info("DCC: Starting MasterDCCTunnel with %s", ownerNick)
			tunnel.Start(conn)
		} else {
			util.Error("DCC: Could not create MasterDCCTunnel - unexpected type returned")
			conn.Close()
		}
	} else {
		util.Debug("Creating standard DCCTunnel for bot %s", b.CurrentNick)
		tunnel := factory.CreateStandardDCCTunnel(b, ownerNick, func() {
			b.dccTunnel = nil
		})

		// Start the tunnel
		if dccTunnel, ok := tunnel.(*dcc.DCCTunnel); ok {
			b.dccTunnel = dccTunnel
			util.Debug("DCC: Starting standard DCC tunnel with %s", ownerNick)
			dccTunnel.Start(conn)
		} else {
			util.Warning("DCC: Unexpected tunnel type created")
			conn.Close()
		}
	}
}

func (b *Bot) handleCTCP(e *irc.Event) {
	util.Debug("CTCP Event | Nick: %s | Args: %v | Message: %s", e.Nick, e.Arguments, e.Message())

	ctcpMessage := e.Message()
	if strings.HasPrefix(ctcpMessage, "DCC ") {
		b.handleDCCRequest(e)
	}
}

func (b *Bot) handlePrivMsg(e *irc.Event) {
	// Handle commands only for MasterBot
	if !b.isDrone {
		b.HandleCommands(e)
	}

	// For all bots, forward messages to tunnel if it exists
	if b.dccTunnel != nil {
		b.ForwardToTunnel(e.Raw)
	}
}

func intToIP(intIP uint32) net.IP {
	return net.IPv4(byte(intIP>>24), byte(intIP>>16), byte(intIP>>8), byte(intIP))
}

func (b *Bot) JoinChannel(channel string) {
	if b.IsConnected() {
		util.Debug("Bot %s is joining channel %s", b.CurrentNick, channel)
		b.Connection.Join(channel)
	} else {
		util.Debug("Bot %s is not connected; cannot join channel %s", b.CurrentNick, channel)
	}
}

func (b *Bot) PartChannel(channel string) {
	if b.IsConnected() {
		util.Debug("Bot %s is leaving channel %s", b.CurrentNick, channel)
		b.Connection.Part(channel)
	} else {
		util.Debug("Bot %s is not connected; cannot part channel %s", b.CurrentNick, channel)
	}
}

func (b *Bot) SendMessage(target, message string) {
	if b.IsConnected() {
		util.Debug("Bot %s is sending message to %s: %s", b.CurrentNick, target, message)
		b.Connection.Privmsg(target, message)
	} else {
		util.Debug("Bot %s is not connected; cannot send message to %s", b.CurrentNick, target)
	}
}

// W bot.go
func (b *Bot) AttemptNickChange(nick string) {
	// Nick catching functionality has been removed
	util.Debug("Nick catching functionality has been removed, ignoring AttemptNickChange call")
}

func (b *Bot) ChangeNick(newNick string) {
	if b.IsConnected() {
		oldNick := b.GetCurrentNick()
		util.Info("Bot %s is attempting to change nick to %s", oldNick, newNick)

		// Set up a channel to receive the result of the nick change
		nickChangeDone := make(chan bool, 1)

		// Add a temporary callback for NICK events
		callbackID := b.Connection.AddCallback("NICK", func(e *irc.Event) {
			if e.Nick == oldNick && e.Message() == newNick {
				// Nick change was successful
				nickChangeDone <- true
			}
		})

		// Add a temporary callback for nick-related errors
		errorCallbackID := b.Connection.AddCallback("433", func(e *irc.Event) {
			// Nick already in use
			if len(e.Arguments) > 1 && e.Arguments[1] == newNick {
				nickChangeDone <- false
			}
		})

		// Cleanup callbacks after we're done
		defer b.Connection.RemoveCallback("NICK", callbackID)
		defer b.Connection.RemoveCallback("433", errorCallbackID)

		// Send the nick change command
		b.Connection.Nick(newNick)

		// Wait for the result with a timeout
		select {
		case success := <-nickChangeDone:
			if success {
				util.Info("Bot successfully changed nick from %s to %s", oldNick, newNick)
				b.updateNickInfo(newNick)
			} else {
				util.Warning("Failed to change nick for bot from %s to %s", oldNick, newNick)
			}
		case <-time.After(5 * time.Second):
			// Timeout - check the current nick status
			currentNick := b.Connection.GetNick()
			if currentNick == newNick {
				util.Info("Bot successfully changed nick from %s to %s", oldNick, newNick)
				b.updateNickInfo(newNick)
			} else {
				util.Warning("Nick change timed out. Current nick: %s", currentNick)
			}
		}
	} else {
		util.Debug("Bot %s is not connected; cannot change nick", b.GetCurrentNick())
	}
}

func (b *Bot) Reconnect() {
	if b.IsConnected() {
		oldNick := b.GetCurrentNick()

		// Store the current nick as previous nick for potential recovery
		b.PreviousNick = oldNick
		util.Debug("Stored previous nick %s for potential recovery", b.PreviousNick)

		b.Quit("Reconnecting")

		time.Sleep(5 * time.Second)

		// First try to reconnect with the same nick
		util.Info("Attempting to reconnect with the same nick: %s", oldNick)
		err := b.connectWithNewNick(oldNick)
		if err == nil {
			util.Info("Bot successfully reconnected with the same nick: %s", oldNick)
			return
		}
		util.Warning("Failed to reconnect with the same nick %s: %v", oldNick, err)

		// If that fails, try with a new random nick
		newNick, username, realname := b.getNewWordsFromPool()
		b.mutex.Lock()
		b.CurrentNick = newNick
		b.Username = username
		b.Realname = realname
		b.mutex.Unlock()

		util.Info("Attempting to reconnect with new nick: %s", newNick)

		// Configure exponential backoff
		maxRetries := b.Config.MaxAttempts
		backoff := util.ExponentialBackoff{
			Initial:  time.Duration(b.GlobalConfig.MinReconnectDelay) * time.Second,
			Max:      time.Duration(b.GlobalConfig.MaxReconnectDelay) * time.Second,
			Factor:   2.0,
			MaxCount: maxRetries,
		}

		// Try to connect with exponential backoff
		for attempt := 0; attempt < maxRetries; attempt++ {
			util.Info("Bot is attempting to reconnect with new nick (attempt %d/%d)",
				attempt+1, maxRetries)

			err := b.Connect()
			if err == nil {
				util.Info("Bot %s reconnected with new nick", b.GetCurrentNick())
				return
			}
			util.Error("Reconnect attempt %d failed: %v", attempt+1, err)

			if attempt == maxRetries-1 {
				// If all attempts failed, try with a shuffled version of the original nick
				shuffledNick := shuffleNick(oldNick)
				util.Info("Attempting to reconnect with shuffled nick: %s", shuffledNick)

				err = b.connectWithNewNick(shuffledNick)
				if err != nil {
					util.Error("Failed to reconnect bot with shuffled nick %s: %v", shuffledNick, err)
				} else {
					util.Info("Bot successfully reconnected with shuffled nick: %s", shuffledNick)
					return
				}
				break
			}

			// Wait with exponential backoff before next attempt
			delay := backoff.NextDelay()
			util.Debug("Waiting %v before next reconnect attempt", delay)
			time.Sleep(delay)
		}

		// If we got here, all reconnect attempts failed
		util.Error("Bot %s could not reconnect after multiple attempts", oldNick)
		b.mutex.Lock()
		b.gaveUp = true
		b.mutex.Unlock()

		if b.botManager != nil {
			if bm, ok := b.botManager.(*BotManager); ok {
				bm.removeBot(b)
			}
		}
	} else {
		util.Debug("Bot %s is not connected; cannot reconnect", b.GetCurrentNick())
	}
}

func (b *Bot) UpdateOwners(owners []string) error {
	b.mutex.Lock()
	defer b.mutex.Unlock()

	b.owners.Owners = owners
	return nil
}

// ConfigureMasterDCCHandler configures special handling for master bots
func (b *Bot) ConfigureMasterDCCHandler() {
	util.Info("Configuring MasterBot DCC handler for %s", b.CurrentNick)

	// Set IsMaster to true and isDrone to false
	b.IsMaster = true
	b.isDrone = false

	util.Info("MasterBot %s configured successfully (IsMaster=true, isDrone=false)", b.CurrentNick)
}

// IsMasterBot returns whether this bot is a master bot
func (b *Bot) IsMasterBot() bool {
	return b.IsMaster
}

func (b *Bot) GetCurrentNick() string {
	if b.Connection != nil {
		return b.Connection.GetNick()
	}
	// Fallback do lokalnego stanu tylko jeśli nie ma połączenia
	b.mutex.Lock()
	defer b.mutex.Unlock()
	return b.CurrentNick
}

func (b *Bot) SetOwnerList(owners auth.OwnerList) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	b.owners = owners
	util.Debug("Bot %s set owners: %v", b.CurrentNick, owners)
}

func (b *Bot) SetChannels(channels []string) {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	b.channels = channels
	util.Debug("Bot %s set channels: %v", b.CurrentNick, channels)
}

func (b *Bot) updatePingStatus() {
	// Funkcja pozostawiona dla kompatybilności, ale nie robi nic
	// Nie używamy już ping jako wyznacznika statusu połączenia
	util.Debug("Bot %s received PING/PONG from server (ping status tracking disabled)", b.CurrentNick)
}

func (b *Bot) startPingChecker() {
	// Funkcja pozostawiona dla kompatybilności, ale zmodyfikowana
	// Nie używamy już ping jako wyznacznika statusu połączenia
	// Zamiast tego, po prostu wysyłamy okresowe pingi, aby utrzymać połączenie

	pingInterval := 2 * time.Minute

	ticker := time.NewTicker(pingInterval)
	defer ticker.Stop()

	for range ticker.C {
		if !b.isConnected.Load() || b.Connection == nil {
			continue
		}

		// Wysyłamy ping do serwera, aby utrzymać połączenie
		// ale nie używamy go do określania statusu połączenia
		if b.Connection.IsFullyConnected() {
			util.Debug("Bot %s sending proactive PING to server (keep-alive only)", b.CurrentNick)
			b.Connection.SendRawf("PING :%d", time.Now().Unix())
		}
	}
}

// Nowa metoda pomocnicza do łączenia z nowym nickiem
func (b *Bot) connectWithNewNick(nick string) error {
	b.mutex.Lock()
	b.CurrentNick = nick
	b.mutex.Unlock()

	return b.Connect()
}

// IsOnChannel sprawdza, czy bot jest aktywny na danym kanale
func (b *Bot) IsOnChannel(channel string) bool {
	if !b.isConnected.Load() || b.Connection == nil {
		return false
	}

	// Sprawdzamy, czy bot jest połączony według biblioteki IRC
	if !b.Connection.IsFullyConnected() {
		return false
	}

	// Jeśli bot jest połączony, zakładamy że jest na kanale #rr
	// ponieważ wszystkie boty dołączają do tego kanału po połączeniu
	if channel == "#rr" {
		return true
	}

	// Dla innych kanałów, sprawdzamy czy bot jest MasterBotem i czy kanał jest na liście
	if b.IsMaster {
		b.mutex.Lock()
		defer b.mutex.Unlock()

		for _, ch := range b.channels {
			if ch == channel {
				return true
			}
		}
	}

	return false
}
