package dcc

import (
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"os/user"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/kofany/pNb/internal/types"
	"github.com/kofany/pNb/internal/util"
)

type NickSyncPayload struct {
	Nicks     []types.NickStatus `json:"nicks"`
	NodeID    string             `json:"node_id"`
	Timestamp time.Time          `json:"timestamp"`
	RequestID string             `json:"request_id"`
}

func (dt *DCCTunnel) processCommand(command string) {
	util.Debug("DCC: Processing command: %s for session: %s on bot %s", command, dt.sessionID, dt.bot.GetCurrentNick())

	fields := strings.Fields(command)
	if len(fields) == 0 {
		util.Warning("DCC: Empty command received from session: %s", dt.sessionID)
		return
	}

	// Remove the '.' prefix
	cmd := strings.TrimPrefix(fields[0], ".")
	cmd = strings.ToLower(cmd)

	// Obsługa błędów w wykonywaniu komend
	defer func() {
		if r := recover(); r != nil {
			errorMsg := fmt.Sprintf("Command execution error: %v", r)
			util.Error("DCC: Panic in command execution: %v", r)
			dt.sendToClient(errorMsg)
		}
	}()

	switch cmd {
	case "msg", "m", "message":
		dt.handleMsgCommand(fields[1:])
	case "join", "j":
		dt.handleJoinCommand(fields[1:])
	case "part", "leave", "p":
		dt.handlePartCommand(fields[1:])
	case "mode":
		dt.handleModeCommand(fields[1:])
	case "kick", "k":
		dt.handleKickCommand(fields[1:])
	case "quit":
		dt.handleQuitCommand(fields[1:])
	case "nick", "n":
		dt.handleNickCommand(fields[1:])
	case "raw":
		dt.handleRawCommand(fields[1:])
	case "help":
		dt.sendHelpMessage()
	case "mjoin":
		// Zmodyfikowano - używa lokalnej funkcjonalności dla wszystkich botów
		dt.handleMassJoinCommand(fields[1:])
	case "mpart":
		// Zmodyfikowano - używa lokalnej funkcjonalności dla wszystkich botów
		dt.handleMassPartCommand(fields[1:])
	case "mreconnect", "mrec":
		// Zmodyfikowano - używa lokalnej funkcjonalności dla wszystkich botów
		dt.handleMassReconnectCommand(fields[1:])
	case "addnick":
		dt.handleAddNickCommand(fields[1:])
	case "delnick":
		dt.handleDelNickCommand(fields[1:])
	case "listnicks":
		dt.handleListNicksCommand(fields[1:])
	case "addowner":
		dt.handleAddOwnerCommand(fields[1:])
	case "delowner":
		dt.handleDelOwnerCommand(fields[1:])
	case "listowners":
		dt.handleListOwnersCommand(fields[1:])
	case "cflo", "cflood":
		dt.handleCfloodCommand(fields[1:])
	case "nflo", "nflood":
		dt.handleNfloodCommand(fields[1:])
	case "info":
		dt.handleInfoCommand(fields[1:])
	case "bots":
		dt.handleBotsCommand(fields[1:])
	case "servers":
		dt.handleServersCommand(fields[1:])
	case "abots":
		// Zmodyfikowano - używa lokalnej funkcjonalności zamiast botnet
		dt.handleAllBotsCommand(fields[1:])
	case "addbots":
		// Nowa komenda do dodawania botów z zewnętrznych list proxy
		dt.handleAddBotsCommand(fields[1:])
	case "stopaddbots":
		// Komenda do zatrzymania procesu dodawania botów
		dt.handleStopAddBotsCommand(fields[1:])
	case "clean":
		// Komenda do czyszczenia niepołączonych botów
		dt.handleCleanCommand(fields[1:])
	default:
		dt.sendToClient(fmt.Sprintf("Unknown command: %s", cmd))
	}
}

func (dt *DCCTunnel) handleMsgCommand(args []string) {
	if len(args) >= 2 {
		target := args[0]
		message := strings.Join(args[1:], " ")

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleMsgCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error sending message: %v", r))
				}
			}()

			dt.bot.SendMessage(target, message)
		})
	} else {
		dt.sendToClient("Usage: .msg <target> <message>")
	}
}

func (dt *DCCTunnel) handleJoinCommand(args []string) {
	if len(args) >= 1 {
		channel := args[0]

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleJoinCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error joining channel: %v", r))
				}
			}()

			dt.bot.JoinChannel(channel)
		})
	} else {
		dt.sendToClient("Usage: .join <channel>")
	}
}

func (dt *DCCTunnel) handlePartCommand(args []string) {
	if len(args) >= 1 {
		channel := args[0]

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handlePartCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error leaving channel: %v", r))
				}
			}()

			dt.bot.PartChannel(channel)
		})
	} else {
		dt.sendToClient("Usage: .part <channel>")
	}
}

func (dt *DCCTunnel) handleModeCommand(args []string) {
	if len(args) >= 2 {
		target := args[0]
		modes := strings.Join(args[1:], " ")
		command := fmt.Sprintf("MODE %s %s", target, modes)

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleModeCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error setting mode: %v", r))
				}
			}()

			dt.bot.SendRaw(command)
		})
	} else if len(args) >= 1 {
		target := args[0]
		command := fmt.Sprintf("MODE %s", target)

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleModeCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error getting mode: %v", r))
				}
			}()

			dt.bot.SendRaw(command)
		})
	} else {
		dt.sendToClient("Usage: .mode <target> [modes] [args]")
	}
}

func (dt *DCCTunnel) handleKickCommand(args []string) {
	if len(args) >= 2 {
		channel := args[0]
		user := args[1]
		reason := ""
		if len(args) > 2 {
			reason = strings.Join(args[2:], " ")
		}
		command := fmt.Sprintf("KICK %s %s :%s", channel, user, reason)

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleKickCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error kicking user: %v", r))
				}
			}()

			dt.bot.SendRaw(command)
		})
	} else {
		dt.sendToClient("Usage: .kick <channel> <user> [reason]")
	}
}

func (dt *DCCTunnel) handleQuitCommand(_ []string) {
	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleQuitCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error quitting: %v", r))
			}
		}()

		dt.bot.Quit("Quit via DCC")
		dt.Stop()
	})
}

func (dt *DCCTunnel) handleNickCommand(args []string) {
	if len(args) >= 1 {
		newNick := args[0]

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleNickCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error changing nick: %v", r))
				}
			}()

			dt.bot.ChangeNick(newNick)
		})
	} else {
		dt.sendToClient("Usage: .nick <newnick>")
	}
}

func (dt *DCCTunnel) handleRawCommand(args []string) {
	if len(args) >= 1 {
		rawCmd := strings.Join(args, " ")

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleRawCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error sending raw command: %v", r))
				}
			}()

			dt.bot.SendRaw(rawCmd)
		})
	} else {
		dt.sendToClient("Usage: .raw <command>")
	}
}

// handleMassJoinCommand obsługuje komendę .mjoin (wszyscy boty dołączają do kanału)
func (dt *DCCTunnel) handleMassJoinCommand(args []string) {
	if len(args) == 0 {
		dt.sendToClient("Usage: .mjoin <channel>")
		return
	}

	channel := args[0]
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleMassJoinCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error executing mass join: %v", r))
			}
		}()

		totalBots := 0
		bots := bm.GetBots() // Kopiujemy listę botów, aby uniknąć problemów z współbieżnością

		dt.sendToClient(fmt.Sprintf("Starting mass join to channel %s for %d bots...", channel, len(bots)))

		for _, bot := range bots {
			if bot.IsConnected() {
				bot.JoinChannel(channel)
				totalBots++
			}
		}

		dt.sendToClient(fmt.Sprintf("Completed: All %d bots are joining channel %s", totalBots, channel))
	})
}

// handleMassPartCommand obsługuje komendę .mpart (wszyscy boty opuszczają kanał)
func (dt *DCCTunnel) handleMassPartCommand(args []string) {
	if len(args) == 0 {
		dt.sendToClient("Usage: .mpart <channel>")
		return
	}

	channel := args[0]
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleMassPartCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error executing mass part: %v", r))
			}
		}()

		totalBots := 0
		bots := bm.GetBots() // Kopiujemy listę botów, aby uniknąć problemów z współbieżnością

		dt.sendToClient(fmt.Sprintf("Starting mass part from channel %s for %d bots...", channel, len(bots)))

		for _, bot := range bots {
			if bot.IsConnected() {
				bot.PartChannel(channel)
				totalBots++
			}
		}

		dt.sendToClient(fmt.Sprintf("Completed: All %d bots are leaving channel %s", totalBots, channel))
	})
}

// handleMassReconnectCommand obsługuje komendę .mreconnect (wszyscy boty się reconnectują)
func (dt *DCCTunnel) handleMassReconnectCommand(_ []string) {
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleMassReconnectCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error executing mass reconnect: %v", r))
			}
		}()

		totalBots := 0
		bots := bm.GetBots() // Kopiujemy listę botów, aby uniknąć problemów z współbieżnością

		dt.sendToClient(fmt.Sprintf("Starting mass reconnect for %d bots...", len(bots)))

		for _, bot := range bots {
			if bot.IsConnected() {
				go bot.Reconnect() // Reconnect już jest asynchroniczny
				totalBots++
			}
		}

		dt.sendToClient(fmt.Sprintf("Completed: All %d bots are reconnecting", totalBots))
	})
}

func (dt *DCCTunnel) handleAddNickCommand(args []string) {
	if len(args) >= 1 {
		nick := args[0]
		if bm := dt.bot.GetBotManager(); bm != nil {
			// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
			util.GoSafe(func() {
				// Dodajemy obsługę paniki
				defer func() {
					if r := recover(); r != nil {
						util.Error("Panic in handleAddNickCommand: %v", r)
						dt.sendToClient(fmt.Sprintf("Error adding nick: %v", r))
					}
				}()

				// Zachowano dodawanie nicka do listy jako informacja
				err := dt.bot.GetNickManager().AddNick(nick)
				if err != nil {
					dt.sendToClient(fmt.Sprintf("Error adding nick: %v", err))
				} else {
					dt.sendToClient(fmt.Sprintf("Nick '%s' has been added to the list.", nick))
				}
			})
		}
	} else {
		dt.sendToClient("Usage: .addnick <nick>")
	}
}

func (dt *DCCTunnel) handleDelNickCommand(args []string) {
	if len(args) >= 1 {
		nick := args[0]
		if bm := dt.bot.GetBotManager(); bm != nil {
			// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
			util.GoSafe(func() {
				// Dodajemy obsługę paniki
				defer func() {
					if r := recover(); r != nil {
						util.Error("Panic in handleDelNickCommand: %v", r)
						dt.sendToClient(fmt.Sprintf("Error removing nick: %v", r))
					}
				}()

				// Zachowano usuwanie nicka z listy jako informacja
				err := dt.bot.GetNickManager().RemoveNick(nick)
				if err != nil {
					dt.sendToClient(fmt.Sprintf("Error removing nick: %v", err))
				} else {
					dt.sendToClient(fmt.Sprintf("Nick '%s' has been removed from the list.", nick))
				}
			})
		}
	} else {
		dt.sendToClient("Usage: .delnick <nick>")
	}
}

func (dt *DCCTunnel) handleListNicksCommand(_ []string) {
	if bm := dt.bot.GetBotManager(); bm != nil {
		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleListNicksCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error listing nicks: %v", r))
				}
			}()

			// Zachowano listowanie nicków jako informacja
			nicks := dt.bot.GetNickManager().GetNicks()
			dt.sendToClient(fmt.Sprintf("Saved nicks (nick monitoring functionality has been removed): %s", strings.Join(nicks, ", ")))
		})
	}
}

func (dt *DCCTunnel) handleAddOwnerCommand(args []string) {
	if len(args) == 0 {
		dt.sendToClient("Usage: .addowner <mask>")
		return
	}

	ownerMask := args[0]
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleAddOwnerCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error adding owner: %v", r))
			}
		}()

		if err := bm.AddOwner(ownerMask); err != nil {
			dt.sendToClient(fmt.Sprintf("Failed to add owner: %v", err))
			return
		}

		dt.sendToClient(fmt.Sprintf("Owner '%s' has been added and will be used by all bots.", ownerMask))
	})
}

func (dt *DCCTunnel) handleDelOwnerCommand(args []string) {
	if len(args) == 0 {
		dt.sendToClient("Usage: .delowner <mask>")
		return
	}

	ownerMask := args[0]
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleDelOwnerCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error removing owner: %v", r))
			}
		}()

		if err := bm.RemoveOwner(ownerMask); err != nil {
			dt.sendToClient(fmt.Sprintf("Failed to remove owner: %v", err))
			return
		}

		dt.sendToClient(fmt.Sprintf("Owner '%s' has been removed from all bots.", ownerMask))
	})
}

func (dt *DCCTunnel) handleListOwnersCommand(_ []string) {
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleListOwnersCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error listing owners: %v", r))
			}
		}()

		owners := bm.GetOwners()
		if len(owners) == 0 {
			dt.sendToClient("No owners defined.")
			return
		}

		dt.sendToClient("Current owners:")
		for _, owner := range owners {
			dt.sendToClient("- " + owner)
		}
	})
}

func ExecuteCfloodCommand(bm types.BotManager, args []string) error {
	if len(args) < 3 {
		return fmt.Errorf("usage: cflo <#channel> <loops> <message>")
	}

	channel := args[0]
	loops, err := strconv.Atoi(args[1])
	if err != nil || loops <= 0 {
		return fmt.Errorf("number of loops must be a positive integer")
	}

	maxLoops := 100
	if loops > maxLoops {
		return fmt.Errorf("maximum number of loops is %d", maxLoops)
	}

	message := strings.Join(args[2:], " ")
	util.Info("Starting flood test with %d loops", loops)
	for _, bot := range bm.GetBots() {
		go executeFlood(bot, channel, loops, message)
	}

	return nil
}

func ExecuteNfloodCommand(bm types.BotManager, args []string) error {
	if len(args) < 3 {
		return fmt.Errorf("usage: nflo <nick> <loops> <message>")
	}

	targetNick := args[0]
	loops, err := strconv.Atoi(args[1])
	if err != nil || loops <= 0 {
		return fmt.Errorf("number of loops must be a positive integer")
	}

	maxLoops := 1000
	if loops > maxLoops {
		return fmt.Errorf("maximum number of loops is %d", maxLoops)
	}

	message := strings.Join(args[2:], " ")
	util.Info("Starting nick flood test with %d loops", loops)
	for _, bot := range bm.GetBots() {
		go executeNickFlood(bot, targetNick, loops, message)
	}

	return nil
}

func (dt *DCCTunnel) handleCfloodCommand(args []string) {
	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleCfloodCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error executing channel flood: %v", r))
			}
		}()

		if err := ExecuteCfloodCommand(dt.botManager, args); err != nil {
			dt.sendToClient(err.Error())
		}
	})
}

func (dt *DCCTunnel) handleNfloodCommand(args []string) {
	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleNfloodCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error executing nick flood: %v", r))
			}
		}()

		if err := ExecuteNfloodCommand(dt.botManager, args); err != nil {
			dt.sendToClient(err.Error())
		}
	})
}

func executeFlood(bot types.Bot, channel string, loops int, message string) {
	// Pobieramy WordPool z BotManager
	var wp *util.WordPool
	if bm := bot.GetBotManager(); bm != nil {
		wp = bm.GetWordPool()
	}

	joinCmd := fmt.Sprintf("JOIN %s", channel)
	bot.SendRaw(joinCmd)
	time.Sleep(500 * time.Millisecond)

	for i := 0; i < loops; i++ {
		privmsgCmd := fmt.Sprintf("PRIVMSG %s :%s", channel, message)
		bot.SendRaw(privmsgCmd)
		time.Sleep(1 * time.Second)

		newNick := util.GenerateFallbackNick(wp) // Przekazujemy WordPool

		bot.ChangeNick(newNick)
		time.Sleep(2 * time.Second)

		bot.SendRaw(privmsgCmd)
		time.Sleep(2 * time.Second)

		newNick = util.GenerateFallbackNick(wp) // Przekazujemy WordPool
		bot.ChangeNick(newNick)
	}

	partCmd := fmt.Sprintf("PART %s", channel)
	bot.SendRaw(partCmd)
	time.Sleep(90 * time.Second)

	newNick := util.GenerateFallbackNick(wp) // Przekazujemy WordPool
	util.Info("==================>>> Zlecam Changenick na ------>>> %s", newNick)
	bot.ChangeNick(newNick)
	time.Sleep(3 * time.Second)
}

func executeNickFlood(bot types.Bot, targetNick string, loops int, message string) {
	// Pobieramy WordPool z BotManager
	var wp *util.WordPool
	if bm := bot.GetBotManager(); bm != nil {
		wp = bm.GetWordPool()
	}

	for i := 0; i < loops; i++ {
		newNick := util.GenerateFallbackNick(wp) // Przekazujemy WordPool

		bot.ChangeNick(newNick)
		time.Sleep(4 * time.Second)

		privmsgCmd := fmt.Sprintf("PRIVMSG %s :%s", targetNick, message)
		bot.SendRaw(privmsgCmd)
	}

	time.Sleep(10 * time.Second)
	newNick := util.GenerateFallbackNick(wp) // Przekazujemy WordPool
	bot.ChangeNick(newNick)
}

func (dt *DCCTunnel) handleInfoCommand(_ []string) {
	if bm := dt.bot.GetBotManager(); bm != nil {
		// Informujemy użytkownika, że rozpoczynamy zbieranie danych
		dt.sendToClient("Collecting system information, please wait...")

		// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
		util.GoSafe(func() {
			// Dodajemy obsługę paniki
			defer func() {
				if r := recover(); r != nil {
					util.Error("Panic in handleInfoCommand: %v", r)
					dt.sendToClient(fmt.Sprintf("Error collecting system information: %v", r))
				}
			}()

			info := dt.generateSystemInfo()
			dt.sendToClient(info)
		})
	}
}

// Funkcje pomocnicze

func (dt *DCCTunnel) generateSystemInfo() string {
	currentUser, err := user.Current()
	if err != nil {
		currentUser = &user.User{}
	}

	cwd, err := os.Getwd()
	if err != nil {
		cwd = "unknown"
	}

	serverHost := dt.bot.GetServerName()
	ips, _ := net.LookupIP(serverHost)
	var ipv4, ipv6 string
	for _, ip := range ips {
		if ip.To4() != nil {
			ipv4 = ip.String()
		} else {
			ipv6 = ip.String()
		}
	}

	externalIPv4 := dt.getExternalIP("tcp4")
	externalIPv6 := dt.getExternalIP("tcp6")

	return fmt.Sprintf(`
Bot Information:
---------------
Current Working Directory: %s
Username: %s
Home Directory: %s

Server Information:
------------------
Server Name: %s
Server IPv4: %s
Server IPv6: %s

External IP Information:
----------------------
External IPv4: %s
External IPv6: %s

Process Information:
------------------
Process ID: %d
Parent Process ID: %d`,
		cwd,
		currentUser.Username,
		currentUser.HomeDir,
		serverHost,
		ipv4,
		ipv6,
		externalIPv4,
		externalIPv6,
		os.Getpid(),
		os.Getppid())
}

func (dt *DCCTunnel) getExternalIP(network string) string {
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			DialContext: func(ctx context.Context, _, addr string) (net.Conn, error) {
				d := net.Dialer{Timeout: 5 * time.Second}
				return d.DialContext(ctx, network, addr)
			},
		},
		Timeout: 5 * time.Second,
	}

	resp, err := client.Get("https://ip.shr.al")
	if err != nil {
		return "unavailable"
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "unavailable"
	}

	return strings.TrimSpace(string(body))
}

// Implementacje funkcji flood

// Pomocnicza funkcja do generowania losowych nicków
func (dt *DCCTunnel) sendHelpMessage() {
	// Wykonujemy operację asynchronicznie, aby nie blokować innych komend
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in sendHelpMessage: %v", r)
				dt.sendToClient(fmt.Sprintf("Error displaying help: %v", r))
			}
		}()

		helpMessage := `
Available commands:
` + colorText("[ Basic ] commands (one bot):", 10) + `
` + colorCommand(".msg", "<target> <message> - Send a message") + `
` + colorCommand(".join", "<channel> - Join a channel") + `
` + colorCommand(".part", "<channel> - Leave a channel") + `
` + colorCommand(".mode", "<target> [modes] - Set channel/user modes") + `
` + colorCommand(".kick", "<channel> <user> [reason] - Kick a user") + `
` + colorCommand(".quit", "Disconnect and close the session") + `
` + colorCommand(".nick", "<new_nick> - Change nickname") + `
` + colorCommand(".raw", "<command> - Send raw IRC command") + `

` + colorText("[ Mass ] commands (all bots):", 10) + `
` + colorCommand(".mjoin", "<channel> - All bots join a channel") + `
` + colorCommand(".mpart", "<channel> - All bots leave a channel") + `
` + colorCommand(".mreconnect", "- Reconnect all bots") + `

` + colorText("[ Admin ] commands:", 10) + `
` + colorCommand(".addnick", "<nick> - Add a nick to monitor") + `
` + colorCommand(".delnick", "<nick> - Remove a nick from monitoring") + `
` + colorCommand(".listnicks", "- List monitored nicks") + `
` + colorCommand(".addowner", "<mask> - Add an owner mask") + `
` + colorCommand(".delowner", "<mask> - Remove an owner mask") + `
` + colorCommand(".listowners", "- List current owners") + `
` + colorCommand(".ison", "<on|off> - Enable/disable ISON monitoring") + `

` + colorText("[ Info ] commands:", 10) + `
` + colorCommand(".info", "- Display system information") + `
` + colorCommand(".bots", "- Show status of all bots (use .bots n/a/c for details)") + `
` + colorCommand(".servers", "- Show servers statistics") + `
` + colorCommand(".abots", "- Display all bots status") + `

` + colorText("[ Advanced ] commands:", 10) + `
` + colorCommand(".cflo", "<channel> <loops> <message> - Channel flood test") + `
` + colorCommand(".nflo", "<nick> <loops> <message> - Nick flood test") + `
` + colorCommand(".addbots", "[4|5] - Continuously download and add new bots from external proxy lists") + `
` + colorCommand(".stopaddbots", "- Stop the continuous bot addition process") + `
` + colorCommand(".clean", "- Clean up disconnected bots") + `

` + colorText("For more help on a specific command, type: .help <command>", 14) + `

`
		dt.sendToClient(helpMessage)
	})
}

func (dt *DCCTunnel) handleBotsCommand(args []string) {
	// Informujemy użytkownika, że rozpoczynamy zbieranie danych
	dt.sendToClient("Collecting bot statistics, please wait...")

	// Wykonujemy całą operację asynchronicznie, aby nie blokować głównego wątku
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleBotsCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error collecting bot statistics: %v", r))
			}
		}()

		bm := dt.bot.GetBotManager()
		if bm == nil {
			dt.sendToClient("BotManager is not available.")
			return
		}

		// Używamy kontekstu z timeoutem, aby zapobiec zbyt długiemu wykonywaniu
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Kanał do sygnalizacji zakończenia
		done := make(chan struct{})

		// Kanały do przekazywania wyników
		type botsResult struct {
			totalCreated   int
			totalNow       int
			totalConnected int
			totalActive    int
			connectedNicks []string
			activeNicks    []string
		}
		resultCh := make(chan botsResult, 1)

		// Uruchamiamy zbieranie danych w osobnej goroutine
		go func() {
			defer close(done)

			bots := bm.GetBots()
			totalCreatedBots := bm.GetTotalCreatedBots()
			totalBotsNow := len(bots)

			// Liczymy w pełni połączone boty według IsConnected()
			totalConnectedBots := 0
			var connectedBotNicks []string
			for _, bot := range bots {
				if bot.IsConnected() {
					totalConnectedBots++
					connectedBotNicks = append(connectedBotNicks, bot.GetCurrentNick())
				}
			}

			// Liczymy boty aktywne na kanale #rr
			totalActiveBotsOnRR := 0
			var activeBotsOnRRNicks []string
			for _, bot := range bots {
				if bot.IsOnChannel("#rr") {
					totalActiveBotsOnRR++
					activeBotsOnRRNicks = append(activeBotsOnRRNicks, bot.GetCurrentNick())
				}
			}

			// Wysyłamy wyniki
			resultCh <- botsResult{
				totalCreated:   totalCreatedBots,
				totalNow:       totalBotsNow,
				totalConnected: totalConnectedBots,
				totalActive:    totalActiveBotsOnRR,
				connectedNicks: connectedBotNicks,
				activeNicks:    activeBotsOnRRNicks,
			}
		}()

		// Czekamy na zakończenie lub timeout
		select {
		case <-done:
			// Operacja zakończona pomyślnie
		case <-ctx.Done():
			dt.sendToClient("Error: Timeout while collecting bot statistics")
			return
		}

		// Pobieramy wyniki
		result := <-resultCh

		// Przygotowujemy odpowiedź w zależności od argumentów
		if len(args) == 0 {
			// Bez dodatkowych argumentów, wyświetlamy podsumowanie
			output := fmt.Sprintf(
				"Total created bots: %d\nTotal bots now: %d\nTotal fully connected bots: %d\nTotal active bots on #rr: %d",
				result.totalCreated, result.totalNow, result.totalConnected, result.totalActive)
			dt.sendToClient(output)
		} else if len(args) == 1 && strings.ToLower(args[0]) == "n" {
			// Wyświetlamy nicki w pełni połączonych botów
			if result.totalConnected == 0 {
				dt.sendToClient("No bots are currently connected.")
			} else {
				dt.sendToClient("Connected bots: " + strings.Join(result.connectedNicks, ", "))
			}
		} else if len(args) == 1 && strings.ToLower(args[0]) == "a" {
			// Wyświetlamy nicki aktywnych botów na kanale #rr
			if result.totalActive == 0 {
				dt.sendToClient("No bots are currently active on #rr.")
			} else {
				dt.sendToClient("Active bots on #rr: " + strings.Join(result.activeNicks, ", "))
			}
		} else if len(args) == 1 && strings.ToLower(args[0]) == "c" {
			// Wyświetlamy porównanie między połączonymi a aktywnymi botami
			dt.sendToClient(fmt.Sprintf("Connected bots: %d, Active bots on #rr: %d, Difference: %d",
				result.totalConnected, result.totalActive, result.totalActive-result.totalConnected))
		} else {
			dt.sendToClient("Usage: .bots or .bots n (show nicks) or .bots a (show active) or .bots c (compare)")
		}
	})
}

func (dt *DCCTunnel) handleServersCommand(args []string) {
	// Informujemy użytkownika, że rozpoczynamy zbieranie danych
	dt.sendToClient("Collecting server statistics, please wait...")

	// Wykonujemy całą operację asynchronicznie, aby nie blokować głównego wątku
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleServersCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error collecting server statistics: %v", r))
			}
		}()

		bm := dt.bot.GetBotManager()
		if bm == nil {
			dt.sendToClient("BotManager is not available.")
			return
		}

		// Używamy kontekstu z timeoutem, aby zapobiec zbyt długiemu wykonywaniu
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Kanał do sygnalizacji zakończenia
		done := make(chan struct{})

		// Kanały do przekazywania wyników
		type serversResult struct {
			serverCounts   map[string]int
			connectedBots  []types.Bot
			foundBot       types.Bot
			foundBotServer string
		}
		resultCh := make(chan serversResult, 1)

		// Uruchamiamy zbieranie danych w osobnej goroutine
		go func() {
			defer close(done)

			bots := bm.GetBots()
			connectedBots := make([]types.Bot, 0)
			for _, bot := range bots {
				if bot.IsConnected() {
					connectedBots = append(connectedBots, bot)
				}
			}

			// Przygotowujemy dane w zależności od argumentów
			serverCounts := make(map[string]int)
			var foundBot types.Bot
			var foundBotServer string

			// Zawsze zbieramy statystyki serwerów
			for _, bot := range connectedBots {
				serverName := bot.GetServerName()
				serverCounts[serverName]++
			}

			// Jeśli podano argument, szukamy konkretnego bota
			if len(args) == 1 {
				botNick := args[0]
				for _, bot := range connectedBots {
					if strings.EqualFold(bot.GetCurrentNick(), botNick) {
						foundBot = bot
						foundBotServer = bot.GetServerName()
						break
					}
				}
			}

			// Wysyłamy wyniki
			resultCh <- serversResult{
				serverCounts:   serverCounts,
				connectedBots:  connectedBots,
				foundBot:       foundBot,
				foundBotServer: foundBotServer,
			}
		}()

		// Czekamy na zakończenie lub timeout
		select {
		case <-done:
			// Operacja zakończona pomyślnie
		case <-ctx.Done():
			dt.sendToClient("Error: Timeout while collecting server statistics")
			return
		}

		// Pobieramy wyniki
		result := <-resultCh

		// Przygotowujemy odpowiedź w zależności od argumentów
		if len(args) == 0 {
			// Bez argumentów, wyświetlamy statystyki serwerów
			if len(result.serverCounts) == 0 {
				dt.sendToClient("No bots are currently connected.")
			} else {
				var outputLines []string

				// Sortujemy serwery dla lepszej czytelności
				var servers []string
				for server := range result.serverCounts {
					servers = append(servers, server)
				}
				sort.Strings(servers)

				for _, server := range servers {
					count := result.serverCounts[server]
					outputLines = append(outputLines, fmt.Sprintf("%s: %d", server, count))
				}
				dt.sendToClient("Server connections:\n" + strings.Join(outputLines, "\n"))
			}
		} else if len(args) == 1 {
			// Jeśli podano argument, traktujemy go jako nick bota
			botNick := args[0]

			if result.foundBot != nil {
				dt.sendToClient(fmt.Sprintf("Bot %s is connected to server: %s", botNick, result.foundBotServer))
			} else {
				dt.sendToClient(fmt.Sprintf("Bot with nick '%s' is not found or not connected.", botNick))
			}
		} else {
			dt.sendToClient("Usage: .servers or .servers <bot_nick>")
		}
	})
}

func colorCommand(command, description string) string {
	return fmt.Sprintf("%s %s", colorText(command, 9), description)
}

func (dt *DCCTunnel) handleAllBotsCommand(_ []string) {
	// Informujemy użytkownika, że rozpoczynamy zbieranie danych
	dt.sendToClient("Collecting detailed bot status, please wait...")

	// Wykonujemy całą operację asynchronicznie, aby nie blokować głównego wątku
	util.GoSafe(func() {
		// Dodajemy obsługę paniki
		defer func() {
			if r := recover(); r != nil {
				util.Error("Panic in handleAllBotsCommand: %v", r)
				dt.sendToClient(fmt.Sprintf("Error collecting bot status: %v", r))
			}
		}()

		bm := dt.bot.GetBotManager()
		if bm == nil {
			dt.sendToClient("BotManager not available")
			return
		}

		// Używamy kontekstu z timeoutem, aby zapobiec zbyt długiemu wykonywaniu
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Kanał do sygnalizacji zakończenia
		done := make(chan struct{})

		// Kanały do przekazywania wyników
		type allBotsResult struct {
			output        string
			totalBots     int
			connectedBots int
		}
		resultCh := make(chan allBotsResult, 1)

		// Uruchamiamy zbieranie danych w osobnej goroutine
		go func() {
			defer close(done)

			// Pobierz lokalny status botów
			statuses := bm.GetBotsStatus()

			// Przygotuj raport
			totalBots := 0
			connectedBots := 0

			output := "\n" + colorText("Bots Status:", 15) + "\n"
			output += colorText("=========================\n", 14)

			// Sortowanie statusów dla lepszej czytelności
			sort.Slice(statuses, func(i, j int) bool {
				// Najpierw sortuj po serwerze
				if statuses[i].Server != statuses[j].Server {
					return statuses[i].Server < statuses[j].Server
				}
				// Potem po nicku
				return statuses[i].BotNick < statuses[j].BotNick
			})

			// Grupuj boty po serwerach
			servers := make(map[string][]types.NodeBotStatus)
			for _, status := range statuses {
				servers[status.Server] = append(servers[status.Server], status)
			}

			// Sortuj serwery
			var serverNames []string
			for server := range servers {
				serverNames = append(serverNames, server)
			}
			sort.Strings(serverNames)

			// Wyświetl status każdego serwera
			for _, server := range serverNames {
				botsForServer := servers[server]
				output += colorText(fmt.Sprintf("\n[Server] %s (%d bots)\n", server, len(botsForServer)), 10)

				for _, status := range botsForServer {
					totalBots++
					statusText := colorText("DISCONNECTED", 4) // Czerwony dla rozłączonych
					if status.Connected {
						statusText = colorText("CONNECTED", 10) // Zielony dla połączonych
						connectedBots++
					}

					output += fmt.Sprintf("  %s - %s\n",
						colorText(status.BotNick, 15),
						statusText)
				}
			}

			output += colorText("\n=========================\n", 14)
			output += colorText(fmt.Sprintf("Total bots: %d, Connected: %d, Disconnected: %d\n",
				totalBots, connectedBots, totalBots-connectedBots), 14)

			// Wysyłamy wyniki
			resultCh <- allBotsResult{
				output:        output,
				totalBots:     totalBots,
				connectedBots: connectedBots,
			}
		}()

		// Czekamy na zakończenie lub timeout
		select {
		case <-done:
			// Operacja zakończona pomyślnie
		case <-ctx.Done():
			dt.sendToClient("Error: Timeout while collecting bot status")
			return
		}

		// Pobieramy wyniki i wysyłamy do klienta
		result := <-resultCh
		dt.sendToClient(result.output)
	})
}
