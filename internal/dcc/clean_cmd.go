package dcc

import (
	"fmt"

	"github.com/kofany/pNb/internal/util"
)

// handleCleanCommand obsługuje komendę .clean (czyszczenie niepołączonych botów)
func (dt *DCCTunnel) handleCleanCommand(_ []string) {
	dt.sendToClient("Starting cleanup of disconnected bots...")

	// Pobieramy BotManager
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Pobieramy liczbę botów przed czyszczeniem
	botsBefore := len(bm.GetBots())

	// Wywołujemy czyszczenie
	util.GoSafe(func() {
		// Wywołujemy czyszczenie
		bm.PerformCleanup()

		// Pobieramy liczbę botów po czyszczeniu
		botsAfter := len(bm.GetBots())
		botsRemoved := botsBefore - botsAfter

		// Informujemy o wyniku
		dt.sendToClient(fmt.Sprintf("Cleanup completed: removed %d disconnected bots. Remaining bots: %d",
			botsRemoved, botsAfter))
	})

	dt.sendToClient("Cleanup process started in background. Results will be reported shortly.")
}
