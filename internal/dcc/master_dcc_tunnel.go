package dcc

import (
	"net"

	"github.com/kofany/pNb/internal/types"
	"github.com/kofany/pNb/internal/util"
)

// MasterDCCTunnel is a specialized DCC tunnel for the MasterBot
type MasterDCCTunnel struct {
	*DCCTunnel                  // Embed the standard DCCTunnel as a pointer
	botManager types.BotManager // Reference to the bot manager
}

// NewMasterDCCTunnel creates a new master DCC tunnel
func NewMasterDCCTunnel(bot types.Bot, ownerNick string, botManager types.BotManager) *MasterDCCTunnel {
	// Create base tunnel
	baseTunnel := NewDCCTunnel(bot, ownerNick, nil)

	// Create master tunnel
	masterTunnel := &MasterDCCTunnel{
		DCCTunnel:  baseTunnel,
		botManager: botManager,
	}

	return masterTunnel
}

// Start overrides the parent's Start method to use specialized MasterBot DCC tunnel functionality
func (mt *MasterDCCTunnel) Start(conn net.Conn) {
	util.Info("MasterDCCTunnel: Starting tunnel for MasterBot using standard DCC tunnel")

	// First, call the parent's Start method to perform standard initialization
	// This will display the welcome message and set up all standard fields
	mt.DCCTunnel.Start(conn)

	// Log additional information about the MasterBot tunnel
	util.Info("MasterDCCTunnel: MasterBot tunnel started successfully for %s", mt.DCCTunnel.bot.GetCurrentNick())
}
