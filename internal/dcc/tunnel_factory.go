package dcc

import (
	"github.com/kofany/pNb/internal/types"
)

// DCCTunnelFactory implements the types.DCCTunnelCreator interface
type DCCTunnelFactory struct{}

// CreateMasterDCCTunnel creates a new MasterDCCTunnel
func (f *DCCTunnelFactory) CreateMasterDCCTunnel(bot types.Bot, ownerNick string, botManager types.BotManager) interface{} {
	return NewMasterDCCTunnel(bot, ownerNick, botManager)
}

// CreateStandardDCCTunnel creates a new standard DCCTunnel
func (f *DCCTunnelFactory) CreateStandardDCCTunnel(bot types.Bot, ownerNick string, onStop func()) interface{} {
	return NewDCCTunnel(bot, ownerNick, onStop)
}

// GetTunnelFactory returns a singleton DCCTunnelFactory instance
func GetTunnelFactory() types.DCCTunnelCreator {
	return &DCCTunnelFactory{}
}
