package dcc

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/kofany/pNb/internal/proxy"
	"github.com/kofany/pNb/internal/util"
)

const (
	// Rozmiar partii botów do dodania
	batchSize = 1000

	// Czas oczekiwania między cyklami dodawania botów (w sekundach)
	cycleWaitTime = 60
)

// Flaga do kontrolowania nieskończonej pętli dodawania botów
var isAddBotsRunning atomic.Bool

// handleAddBotsCommand obsługuje komendę .addbots (dodawanie nowych botów z zewnętrznych list proxy)
func (dt *DCCTunnel) handleAddBotsCommand(args []string) {
	// Sprawdzamy, czy proces już działa
	if isAddBotsRunning.Load() {
		dt.sendToClient("Bot addition process is already running. Use .stopaddbots to stop it.")
		return
	}

	dt.sendToClient("Starting continuous bot addition process...")

	// Pobieramy BotManager
	bm := dt.bot.GetBotManager()
	if bm == nil {
		dt.sendToClient("BotManager not available")
		return
	}

	// Ustawiamy flagę, że proces jest uruchomiony
	isAddBotsRunning.Store(true)

	// Uruchamiamy proces w osobnym wątku, aby nie blokować interfejsu DCC
	util.GoSafe(func() {
		defer func() {
			isAddBotsRunning.Store(false)
			dt.sendToClient("Continuous bot addition process has been stopped.")
		}()

		// Określamy typy proxy do pobrania
		proxyTypes := []proxy.ProxyType{proxy.Socks4, proxy.Socks5}
		if len(args) > 0 {
			// Jeśli podano argument, używamy tylko określonego typu proxy
			switch args[0] {
			case "4":
				proxyTypes = []proxy.ProxyType{proxy.Socks4}
				dt.sendToClient("Using only SOCKS4 proxies for continuous addition")
			case "5":
				proxyTypes = []proxy.ProxyType{proxy.Socks5}
				dt.sendToClient("Using only SOCKS5 proxies for continuous addition")
			case "stop":
				dt.sendToClient("Invalid argument. Use .stopaddbots to stop the process.")
				return
			default:
				dt.sendToClient(fmt.Sprintf("Unknown proxy type: %s, using both SOCKS4 and SOCKS5", args[0]))
			}
		}

		// Główna pętla nieskończona
		cycleCount := 0
		for isAddBotsRunning.Load() {
			cycleCount++
			dt.sendToClient(fmt.Sprintf("=== Starting bot addition cycle #%d ===", cycleCount))

			// Inicjalizacja ProxyDownloader dla każdego cyklu
			downloader, err := proxy.NewProxyDownloader()
			if err != nil {
				dt.sendToClient(fmt.Sprintf("Failed to initialize proxy downloader: %v", err))
				dt.sendToClient(fmt.Sprintf("Waiting %d seconds before next cycle...", cycleWaitTime))
				time.Sleep(time.Duration(cycleWaitTime) * time.Second)
				continue
			}

			// Pobieramy proxy dla każdego typu
			var allProxies []proxy.ProxyEntry
			var wg sync.WaitGroup
			var mu sync.Mutex

			for _, proxyType := range proxyTypes {
				if !isAddBotsRunning.Load() {
					break
				}

				wg.Add(1)
				util.GoSafe(func() {
					defer wg.Done()

					dt.sendToClient(fmt.Sprintf("Downloading %s proxies...", proxyType))
					proxies, err := downloader.DownloadAndProcessProxies(proxyType)
					if err != nil {
						dt.sendToClient(fmt.Sprintf("Error downloading %s proxies: %v", proxyType, err))
						return
					}

					dt.sendToClient(fmt.Sprintf("Downloaded %d %s proxies", len(proxies), proxyType))

					mu.Lock()
					allProxies = append(allProxies, proxies...)
					mu.Unlock()
				})
			}

			// Czekamy na zakończenie pobierania
			wg.Wait()

			// Sprawdzamy, czy proces został zatrzymany podczas pobierania
			if !isAddBotsRunning.Load() {
				break
			}

			if len(allProxies) == 0 {
				dt.sendToClient("No proxies downloaded in this cycle. Waiting before next attempt...")
				dt.sendToClient(fmt.Sprintf("Waiting %d seconds before next cycle...", cycleWaitTime))
				time.Sleep(time.Duration(cycleWaitTime) * time.Second)
				continue
			}

			dt.sendToClient(fmt.Sprintf("Downloaded a total of %d proxies. Adding new bots in batches of %d...", len(allProxies), batchSize))

			// Dodajemy boty w partiach
			totalAdded := 0
			totalBatches := (len(allProxies) + batchSize - 1) / batchSize // Zaokrąglenie w górę

			dt.sendToClient(fmt.Sprintf("[LOG] Rozpoczynam batchowanie proxy: %d batchy po maks %d proxy", totalBatches, batchSize))

			for i := 0; i < len(allProxies); i += batchSize {
				if !isAddBotsRunning.Load() {
					break
				}

				end := i + batchSize
				if end > len(allProxies) {
					end = len(allProxies)
				}

				batchProxies := allProxies[i:end]
				batchNum := (i / batchSize) + 1

				dt.sendToClient(fmt.Sprintf("[LOG] Start dodawania batcha %d/%d (%d proxy)", batchNum, totalBatches, len(batchProxies)))

				// Dodajemy nowe boty
				count, err := bm.AddNewBots(batchProxies)
				if err != nil {
					dt.sendToClient(fmt.Sprintf("[LOG] Błąd dodawania botów w batchu %d: %v", batchNum, err))
					continue
				}

				totalAdded += count
				dt.sendToClient(fmt.Sprintf("[LOG] Dodano %d botów w batchu %d/%d. Suma: %d", count, batchNum, totalBatches, totalAdded))

				// Informujemy o aktualnej liczbie botów
				dt.sendToClient(fmt.Sprintf("[LOG] Aktualna liczba botów: %d", len(bm.GetBots())))

				// Pobieramy listę nowo dodanych botów
				allBots := bm.GetBots()
				newBots := allBots[len(allBots)-count:]

				dt.sendToClient(fmt.Sprintf("[LOG] Start łączenia botów z batcha %d/%d (max 1000 równocześnie, timeout 2 minuty)...", batchNum, totalBatches))
				startTime := time.Now()
				connectedCount := bm.StartBotsAndWait(newBots)
				duration := time.Since(startTime)

				dt.sendToClient(fmt.Sprintf("[LOG] Połączono %d/%d botów z batcha %d w %v", connectedCount, count, batchNum, duration))

				if duration >= 2*time.Minute {
					dt.sendToClient("[LOG] Timeout 2 minuty osiągnięty. Przechodzę do kolejnego batcha po czyszczeniu niepołączonych botów.")
				}

				if batchNum < totalBatches && isAddBotsRunning.Load() {
					dt.sendToClient("[LOG] Czekam 5 sekund przed kolejnym batchem...")
					time.Sleep(5 * time.Second)
				}

				dt.sendToClient(fmt.Sprintf("[LOG] Zakończono batch %d/%d", batchNum, totalBatches))
			}

			dt.sendToClient(fmt.Sprintf("[LOG] Zakończono cykl #%d: Dodano i połączono %d nowych botów", cycleCount, totalAdded))
			dt.sendToClient(fmt.Sprintf("Waiting %d seconds before starting next cycle...", cycleWaitTime))

			// Czekamy przed rozpoczęciem kolejnego cyklu
			for i := 0; i < cycleWaitTime; i += 5 {
				if !isAddBotsRunning.Load() {
					break
				}
				time.Sleep(5 * time.Second)
			}
		}
	})

	dt.sendToClient("Continuous bot addition process started in background. Use .stopaddbots to stop it.")
}

// handleStopAddBotsCommand obsługuje komendę .stopaddbots (zatrzymanie procesu dodawania botów)
func (dt *DCCTunnel) handleStopAddBotsCommand(_ []string) {
	if !isAddBotsRunning.Load() {
		dt.sendToClient("Bot addition process is not running.")
		return
	}

	dt.sendToClient("Stopping continuous bot addition process... This may take a moment to complete.")
	isAddBotsRunning.Store(false)

	// Czekamy chwilę, aby proces mógł się zakończyć
	go func() {
		// Sprawdzamy co sekundę przez 10 sekund, czy proces się zakończył
		for i := 0; i < 10; i++ {
			if !isAddBotsRunning.Load() {
				dt.sendToClient("Bot addition process has been successfully stopped.")
				return
			}
			time.Sleep(1 * time.Second)
		}

		// Jeśli po 10 sekundach proces nadal działa, informujemy użytkownika
		if isAddBotsRunning.Load() {
			dt.sendToClient("Bot addition process is still shutting down. It will stop after completing current operation.")
		}
	}()
}
