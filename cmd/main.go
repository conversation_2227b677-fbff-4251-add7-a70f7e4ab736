package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/fatih/color"
	"github.com/kofany/pNb/internal/auth"
	"github.com/kofany/pNb/internal/bot"
	"github.com/kofany/pNb/internal/config"
	"github.com/kofany/pNb/internal/nickmanager"
	"github.com/kofany/pNb/internal/proxy"
	"github.com/kofany/pNb/internal/util"
	"github.com/sevlyar/go-daemon"
)

var version = "v1.4.0"

var (
	devMode         = flag.Bool("dev", false, "run in development mode (non-daemon)")
	versionFlag     = flag.Bool("v", false, "show version")
	versionFlagLong = flag.Bool("version", false, "show version")
)

const banner = `
	>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> [ phantom Node bot ]
                 ___      __             __
    ____  ____  [ m ]__  / /_  __  __   / /____  ____ _____ ___
   / __ \/ __ \  / / _ \/ __ \/ / / /  / __/ _ \/ __ ` + "`" + `/ __ ` + "`" + `__ \
  / /_/ / /_/ / / /  __/ /_/ / /_/ /  / /_/  __/ /_/ / / / / / /
 / .___/\____/_/ /\___/_.___/\__, /blo\__/\___/\__,_/_/ /_/ /_/
/_/  ruciu  /___/   dominik /____/                     kofany


`

func logLevelToString(level util.LogLevel) string {
	switch level {
	case util.DEBUG:
		return "DEBUG"
	case util.INFO:
		return "INFO"
	case util.WARNING:
		return "WARNING"
	case util.ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

func printBanner() {
	color.Cyan(banner)
}

func printVersion() {
	versionText := fmt.Sprintf("%s %s %s.",
		color.MagentaString("[pNb]"),
		color.GreenString("phantom Node Bot by kofany"),
		color.YellowString(version),
	)
	fmt.Println(versionText)
}

// Zmienne globalne do przechowywania nazw plików PID i log oraz webserver
var (
	pidFilename string
	logFilename string
)

func main() {
	var botManager *bot.BotManager
	flag.Parse()

	if *versionFlag || *versionFlagLong {
		printVersion()
		os.Exit(0)
	}

	printBanner()
	color.Blue("Starting main function")

	// Inicjalizacja konfiguracji
	color.Blue("Checking and creating config files")
	if err := config.CheckAndCreateConfigFiles(); err != nil {
		color.Red("Error checking/creating config files: %v", err)
		os.Exit(1)
	}
	color.Green("Config files checked and created if necessary")

	// Obsługa trybu demonizowania
	if !*devMode {
		color.Yellow("Starting in daemon mode")
		cntxt := &daemon.Context{
			PidFileName: "",
			PidFilePerm: 0644,
			LogFileName: "",
			LogFilePerm: 0640,
			WorkDir:     "./",
			Umask:       027,
		}
		d, err := cntxt.Reborn()
		if err != nil {
			color.Red("Unable to run: %v", err)
			os.Exit(1)
		}
		if d != nil {
			color.Green("[pNb] phantom Node Bot by kofany %s is running in background with pid: %d.", version, d.Pid)
			return
		}
		defer cntxt.Release()
		log.Print("Daemon started")

		pid := os.Getpid()
		pidFilename = fmt.Sprintf("nr_%d.pid", pid)

		pidFile, err := os.OpenFile(pidFilename, os.O_RDWR|os.O_CREATE, 0644)
		if err != nil {
			color.Red("Failed to create PID file: %v", err)
			os.Exit(1)
		}
		defer pidFile.Close()

		_, err = fmt.Fprintf(pidFile, "%d", pid)
		if err != nil {
			color.Red("Failed to write to PID file: %v", err)
			os.Exit(1)
		}

		logFilename = fmt.Sprintf("nr_%d.log", pid)

		logFile, err := os.OpenFile(logFilename, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0644)
		if err != nil {
			color.Red("Failed to open log file: %v", err)
			os.Exit(1)
		}
		defer logFile.Close()

		os.Stdout = logFile
		os.Stderr = logFile
		log.SetOutput(logFile)

	} else {
		color.Yellow("Starting in development mode")
	}

	// Wczytywanie konfiguracji
	color.Blue("Loading configuration from YAML file")
	cfg, err := config.LoadConfig("configs/config.yaml")
	if err != nil {
		color.Red("Failed to load configuration: %v", err)
		os.Exit(1)
	}
	color.Green("Configuration loaded successfully")

	// Konfiguracja loggera
	var level util.LogLevel
	if !*devMode {
		level = util.WARNING
		color.Yellow("Log level set to WARNING in daemon mode")
	} else {
		color.Blue("Parsing log level from config")
		level, err = util.ParseLogLevel(cfg.Global.LogLevel)
		if err != nil {
			color.Red("Invalid log level in config: %v", err)
			os.Exit(1)
		}
		color.Green("Log level set to %s", logLevelToString(level))
	}

	if *devMode {
		// W trybie deweloperskim nie używamy pliku, ale musimy podać jakąś nazwę
		// Faktycznie plik nie zostanie utworzony
		logFilename = ""
	} else if logFilename == "" {
		logFilename = "bot.log"
	}

	if *devMode {
		color.Blue("Initializing logger in dev mode (no file logging)")
		if err := util.InitLogger(level, logFilename, true); err != nil {
			color.Red("Failed to initialize logger: %v", err)
			os.Exit(1)
		}
	} else {
		color.Blue("Initializing logger with file: %s", logFilename)
		if err := util.InitLogger(level, logFilename, false); err != nil {
			color.Red("Failed to initialize logger: %v", err)
			os.Exit(1)
		}
	}
	defer util.CloseLogger()

	util.Info("Logger initialized with level: %s", logLevelToString(level))

	// Wczytywanie właścicieli
	color.Blue("Loading owners from JSON file")
	owners, err := auth.LoadOwners("configs/owners.json")
	if err != nil {
		color.Red("Failed to load owners: %v", err)
		os.Exit(1)
	}
	util.Debug("Owners loaded: %+v", owners)

	// Inicjalizacja NickManagera
	color.Blue("Creating and initializing NickManager")
	nm := nickmanager.NewNickManager()
	if err := nm.LoadNicks("data/nicks.json"); err != nil {
		color.Red("Failed to load nicks: %v", err)
		os.Exit(1)
	}

	color.Blue("Starting in proxy mode")

	proxyLoader, err := proxy.NewProxyLoader()
	if err != nil {
		color.Red("Failed to initialize proxy loader: %v", err)
		os.Exit(1)
	}

	proxies, err := proxyLoader.LoadAndProcessProxies()
	if err != nil {
		color.Red("Failed to load and process proxies: %v", err)
		os.Exit(1)
	}
	util.Info("Loaded and processed %d proxies", len(proxies))

	botGenerator := proxy.NewBotGenerator(&cfg.Global)
	proxyBotsConfigs, err := botGenerator.GenerateBotsConfigs(proxies)
	if err != nil {
		color.Red("Failed to generate proxy bots configurations: %v", err)
		os.Exit(1)
	}

	total, available, used := botGenerator.GetStats()
	color.Green("WordPool statistics:")
	color.Green("- Total words: %d", total)
	color.Green("- Available words: %d", available)
	color.Green("- Used words: %d", used)

	util.Info("Generated configurations for %d proxy bots", len(proxyBotsConfigs))
	botManager = bot.NewBotManager(cfg, owners, nm, proxyBotsConfigs)
	color.Green("Created bot manager in proxy mode")

	// MasterBot jest już inicjalizowany w BotManager, więc nie musimy go tutaj inicjalizować
	if cfg.Global.MasterBot.Enabled {
		color.Blue("MasterBot will be initialized by BotManager")
		// Daj trochę czasu na inicjalizację przed uruchomieniem innych botów
		time.Sleep(2 * time.Second)
	}

	color.Blue("Starting bots in proxy mode")
	go botManager.StartBots()

	util.Debug("Configuration loaded: %+v", cfg)

	// Obsługa sygnałów do czystego zamykania
	color.Blue("Setting up signal handling for clean shutdown")
	sigs := make(chan os.Signal, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)

	color.Green("Bot is running. Press Ctrl+C to exit.")
	color.Blue("Waiting for shutdown signal")
	<-sigs

	color.Yellow("Shutdown signal received")
	botManager.Stop()
	util.Info("Application has been shut down.")
	color.Green("Application has been shut down.")

	// Usuwanie pliku PID przy zamykaniu
	if !*devMode && pidFilename != "" {
		err := os.Remove(pidFilename)
		if err != nil {
			color.Red("Failed to remove PID file: %v", err)
		} else {
			color.Green("PID file %s removed.", pidFilename)
		}
	}
}
