<!-- Generated by SpecStory -->

# Dodawanie nowych list proxy dla bot<PERSON> (2025-03-20 12:16:54)

_**User**_

Zaplanuj dodawanie nowych  list proxy dla botów podczas działania aplikacji z poziomy dcc z masterbotem przykład.
.load plikz_proxy.txt (tutaj ma być ładowowany pli z folderu którym uruchomiliśmy nasze pNb a nie z data/ )

Ma pododować taki sam proces jak przy starcie aplikacji dla proxy.txt ale dodawać boty do istniejących. po prostu

---

_**Assistant**_

# Plan dodawania nowych list proxy dla botów z poziomu DCC MasterBota

Mogę zaplanować funkcjonalność ładowania proxy z dowolnego pliku dla MasterBota poprzez DCC. Oto mój plan implementacji:

## 1. <PERSON><PERSON><PERSON> now<PERSON> komendy DCC do MasterDCCTunnel

Najpierw dodajmy nową komendę `.load` do obsługi w `master_dcc_tunnel.go`:

```go:internal/dcc/master_dcc_tunnel.go
// ... existing code ...
func (mt *MasterDCCTunnel) processCommand(command string) {
    // ... existing code ...
    
    // Add the new command
    case strings.HasPrefix(command, ".load "):
        args := strings.Fields(command)[1:]
        mt.handleLoadProxyCommand(args)
    
    // ... existing code ...
}

// handleLoadProxyCommand loads proxies from a file and creates new bots
func (mt *MasterDCCTunnel) handleLoadProxyCommand(args []string) {
    if len(args) < 1 {
        mt.sendToClient("Usage: .load <proxy_file_path>")
        return
    }
    
    filePath := args[0]
    mt.sendToClient(fmt.Sprintf("Starting to load proxies from: %s", filePath))
    
    // Delegate to a helper function that will handle the actual loading
    go func() {
        count, err := mt.loadProxiesFromFile(filePath)
        if err != nil {
            mt.sendToClient(fmt.Sprintf("Error loading proxies: %v", err))
            return
        }
        mt.sendToClient(fmt.Sprintf("Successfully loaded %d new bots from proxy file", count))
    }()
}

// loadProxiesFromFile loads proxies from a file and adds bots to the botManager
func (mt *MasterDCCTunnel) loadProxiesFromFile(filePath string) (int, error) {
    // Get the absolute path if not already
    if !filepath.IsAbs(filePath) {
        absPath, err := filepath.Abs(filePath)
        if err == nil {
            filePath = absPath
        }
    }
    
    // Check if the file exists
    if _, err := os.Stat(filePath); os.IsNotExist(err) {
        return 0, fmt.Errorf("file does not exist: %s", filePath)
    }
    
    // Create a proxy loader
    proxyLoader, err := proxy.NewProxyLoader()
    if err != nil {
        return 0, fmt.Errorf("failed to create proxy loader: %v", err)
    }
    
    // Load and process proxies from the specified file
    proxies, err := proxyLoader.LoadAndProcessProxiesFromFile(filePath)
    if err != nil {
        return 0, fmt.Errorf("failed to load proxies: %v", err)
    }
    
    // No proxies found
    if len(proxies) == 0 {
        return 0, fmt.Errorf("no valid proxies found in file")
    }
    
    // Generate bot configs from the proxies
    globalConfig := mt.botManager.GetGlobalConfig()
    botGenerator := proxy.NewBotGenerator(globalConfig)
    botConfigs, err := botGenerator.GenerateBotsConfigs(proxies)
    if err != nil {
        return 0, fmt.Errorf("failed to generate bot configs: %v", err)
    }
    
    // Use reflection to access the unexported addNewBotsFromConfigs method in BotManager
    // Alternatively, you may want to add a public method to BotManager for this purpose
    bm := mt.botManager.(*bot.BotManager) // Type assertion
    
    // Call a new method we'll add to BotManager
    count, err := bm.AddBotsFromConfigs(botConfigs)
    if err != nil {
        return 0, fmt.Errorf("failed to add bots: %v", err)
    }
    
    return count, nil
}
```

## 2. Dodanie metody w ProxyLoader do ładowania z dowolnego pliku

Rozszerzymy `proxy_loader.go` o funkcję ładowania z dowolnego pliku:

```go:internal/proxy/proxy_loader.go
// LoadAndProcessProxiesFromFile loads and processes proxies from a specific file
func (pl *ProxyLoader) LoadAndProcessProxiesFromFile(filePath string) ([]ProxyEntry, error) {
    file, err := os.Open(filePath)
    if err != nil {
        return nil, err
    }
    defer file.Close()

    var proxies []ProxyEntry
    scanner := bufio.NewScanner(file)

    for scanner.Scan() {
        line := strings.TrimSpace(scanner.Text())
        if line == "" {
            continue
        }

        proxy, err := pl.parseProxyLine(line)
        if err != nil {
            util.Warning("Error parsing proxy line: %v", err)
            continue
        }

        servers := pl.iLineLookup.GetServersForIP(proxy.Host)
        proxy.Servers = servers
        proxies = append(proxies, proxy)
    }

    return proxies, scanner.Err()
}
```

## 3. Dodanie metody w BotManager do dodawania nowych botów

Dodajmy nową metodę w `bot/manager.go` do dodawania nowych botów z konfiguracji:

```go:internal/bot/manager.go
// AddBotsFromConfigs generates and adds new bots from the provided configurations
func (bm *BotManager) AddBotsFromConfigs(botsConfigs []config.BotConfig) (int, error) {
    bm.mutex.Lock()
    defer bm.mutex.Unlock()
    
    count := 0
    for _, botConfig := range botsConfigs {
        // Create new bot
        newBot := NewBot(&botConfig, bm.globalConfig, bm.nickManager, bm)
        
        // Configure bot as drone (not master)
        newBot.isDrone = true
        
        // Add to manager
        bm.bots = append(bm.bots, newBot)
        
        // Start the bot in a separate goroutine
        go bm.startBotWithLimitedRetries(newBot)
        
        count++
        bm.totalCreatedBots++
        
        // Optional delay between starting bots to avoid overwhelming resources
        time.Sleep(time.Duration(bm.globalConfig.BotStartDelay) * time.Second)
    }
    
    return count, nil
}
```

## 4. Aktualizacja menu pomocy w MasterDCCTunnel

Dodajmy też informację o nowej komendzie w help message:

```go:internal/dcc/master_dcc_tunnel.go
func (mt *MasterDCCTunnel) sendMasterHelpMessage() {
    // ... existing code ...
    
    // Add the new command to the help message
    helpMsg.WriteString("\n")
    helpMsg.WriteString(colorCommand(".load <file>", "Load proxies from file and create new bots"))
    
    // ... existing code ...
}
```

## 5. Dodanie obsługi błędów i logowania

Upewnijmy się, że wszystkie operacje są odpowiednio logowane, aby użytkownik był informowany o postępie i ewentualnych błędach:

```go:internal/dcc/master_dcc_tunnel.go
// Dla odpowiedniego logowania podczas procesu ładowania
func (mt *MasterDCCTunnel) loadProxiesFromFile(filePath string) (int, error) {
    // ... existing code from earlier ...
    
    mt.sendToClient(fmt.Sprintf("Loading proxies from file: %s", filePath))
    proxies, err := proxyLoader.LoadAndProcessProxiesFromFile(filePath)
    if err != nil {
        util.Error("Failed to load proxies from file %s: %v", filePath, err)
        return 0, err
    }
    
    mt.sendToClient(fmt.Sprintf("Loaded %d proxies from file", len(proxies)))
    
    // ... remaining code ...
    
    mt.sendToClient(fmt.Sprintf("Generated %d bot configurations", len(botConfigs)))
    
    // ... remaining code ...
}
```

## Podsumowanie planu

Ta implementacja pozwoli na:

1. Ładowanie nowych proxy z dowolnego pliku podanego jako argument komendy `.load`
2. Przetwarzanie tych proxy tak samo jak przy starcie aplikacji
3. Generowanie konfiguracji botów na podstawie nowych proxy
4. Dodawanie nowych botów do istniejącego BotManager i uruchamianie ich
5. Informowanie użytkownika o postępie i statusie całego procesu

Wszystko to będzie dostępne za pomocą prostej komendy `.load <scieżka_do_pliku>` wywoływanej w czacie DCC z MasterBotem.

---

