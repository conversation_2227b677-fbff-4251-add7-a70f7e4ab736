#### RFC 2810 - Internet Relay Chat: Architecture

Network Working Group                                           C. Kalt
Request for Comments: 2810                                   April 2000
Updates: 1459
Category: Informational


                   Internet Relay Chat: Architecture

Status of this Memo

   This memo provides information for the Internet community.  It does
   not specify an Internet standard of any kind.  Distribution of this
   memo is unlimited.

Copyright Notice

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

Abstract

   The IRC (Internet Relay Chat) protocol is for use with text based
   conferencing. It has been developed since 1989 when it was originally
   implemented as a mean for users on a BBS to chat amongst themselves.

   First formally documented in May 1993 by RFC 1459 [IRC], the protocol
   has kept evolving. This document is an update describing the
   architecture of the current IRC protocol and the role of its
   different components.  Other documents describe in detail the
   protocol used between the various components defined here.

Table of Contents

   1.  Introduction ...............................................   2
   2.  Components .................................................   2
      2.1  Servers ................................................   2
      2.2  Clients ................................................   3
         2.2.1  User Clients ......................................   3
         2.2.2  Service Clients ...................................   3
   3.  Architecture ...............................................   3
   4.  IRC Protocol Services ......................................   4
      4.1  Client Locator .........................................   4
      4.2  Message Relaying .......................................   4
      4.3  Channel Hosting And Management .........................   4
   5.  IRC Concepts ...............................................   4
      5.1  One-To-One Communication ...............................   5
      5.2  One-To-Many ............................................   5
         5.2.1  To A Channel ......................................   5
         5.2.2  To A Host/Server Mask .............................   6

         5.2.3  To A List .........................................   6
      5.3  One-To-All .............................................   6
         5.3.1  Client-to-Client ..................................   6
         5.3.2  Client-to-Server ..................................   7
         5.3.3  Server-to-Server ..................................   7
   6.  Current Problems ...........................................   7
      6.1  Scalability ............................................   7
      6.2  Reliability ............................................   7
      6.3  Network Congestion .....................................   7
      6.4  Privacy ................................................   8
   7.  Security Considerations ....................................   8
   8.  Current Support And Availability ...........................   8
   9.  Acknowledgements ...........................................   8
   10.  References ................................................   8
   11.  Author's Address ..........................................   9
   12.  Full Copyright Statement ..................................  10

1. Introduction

   The IRC (Internet Relay Chat) protocol has been designed over a
   number of years for use with text based conferencing.  This document
   describes its current architecture.

   The IRC Protocol is based on the client-server model, and is well
   suited to running on many machines in a distributed fashion.  A
   typical setup involves a single process (the server) forming a
   central point for clients (or other servers) to connect to,
   performing the required message delivery/multiplexing and other
   functions.

   This distributed model, which requires each server to have a copy
   of the global state information, is still the most flagrant problem
   of the protocol as it is a serious handicap, which limits the maximum
   size a network can reach.  If the existing networks have been able to
   keep growing at an incredible pace, we must thank hardware
   manufacturers for giving us ever more powerful systems.

2. Components

   The following paragraphs define the basic components of the IRC
   protocol.

2.1 Servers

   The server forms the backbone of IRC as it is the only component
   of the protocol which is able to link all the other components
   together: it provides a point to which clients may connect to talk to

   each other [IRC-CLIENT], and a point for other servers to connect to
   [IRC-SERVER].  The server is also responsible for providing the basic
   services defined by the IRC protocol.

2.2 Clients

   A client is anything connecting to a server that is not another
   server.  There are two types of clients which both serve a different
   purpose.

2.2.1 User Clients

   User clients are generally programs providing a text based
   interface that is used to communicate interactively via IRC.  This
   particular type of clients is often referred as "users".

2.2.2 Service Clients

   Unlike users, service clients are not intended to be used manually
   nor for talking.  They have a more limited access to the chat
   functions of the protocol, while optionally having access to more
   private data from the servers.

   Services are typically automatons used to provide some kind of
   service (not necessarily related to IRC itself) to users.  An example
   is a service collecting statistics about the origin of users
   connected on the IRC network.

3. Architecture

   An IRC network is defined by a group of servers connected to each
   other.  A single server forms the simplest IRC network.

   The only network configuration allowed for IRC servers is that of
   a spanning tree where each server acts as a central node for the rest
   of the network it sees.

                       1--\
                           A        D---4
                       2--/ \      /
                             B----C
                            /      \
                           3        E

   Servers: A, B, C, D, E         Clients: 1, 2, 3, 4

                    [ Fig. 1. Sample small IRC network ]

   The IRC protocol provides no mean for two clients to directly
   communicate.  All communication between clients is relayed by the
   server(s).

4. IRC Protocol Services

   This section describes the services offered by the IRC protocol.  The
   combination of these services allow real-time conferencing.

4.1 Client Locator

   To be able to exchange messages, two clients must be able to locate
   each other.

   Upon connecting to a server, a client registers using a label which
   is then used by other servers and clients to know where the client is
   located.  Servers are responsible for keeping track of all the labels
   being used.

4.2 Message Relaying

   The IRC protocol provides no mean for two clients to directly
   communicate.  All communication between clients is relayed by the
   server(s).

4.3 Channel Hosting And Management

   A channel is a named group of one or more users which will all
   receive messages addressed to that channel.  A channel is
   characterized by its name and current members, it also has a set of
   properties which can be manipulated by (some of) its members.

   Channels provide a mean for a message to be sent to several clients.
   Servers host channels, providing the necessary message multiplexing.
   Servers are also responsible for managing channels by keeping track
   of the channel members.  The exact role of servers is defined in
   "Internet Relay Chat: Channel Management" [IRC-CHAN].

5. IRC Concepts

   This section is devoted to describing the actual concepts behind the
   organization of the IRC protocol and how different classes of
   messages are delivered.

5.1 One-To-One Communication

   Communication on a one-to-one basis is usually performed by clients,
   since most server-server traffic is not a result of servers talking
   only to each other.  To provide a means for clients to talk to each
   other, it is REQUIRED that all servers be able to send a message in
   exactly one direction along the spanning tree in order to reach any
   client.  Thus the path of a message being delivered is the shortest
   path between any two points on the spanning tree.

   The following examples all refer to Figure 1 above.

   Example 1: A message between clients 1 and 2 is only seen by server
       A, which sends it straight to client 2.

   Example 2: A message between clients 1 and 3 is seen by servers A &
       B, and client 3.  No other clients or servers are allowed see the
       message.

   Example 3: A message between clients 2 and 4 is seen by servers A, B,
       C & D and client 4 only.

5.2 One-To-Many

   The main goal of IRC is to provide a forum which allows easy and
   efficient conferencing (one to many conversations).  IRC offers
   several means to achieve this, each serving its own purpose.

5.2.1 To A Channel

      In IRC the channel has a role equivalent to that of the multicast Expand
   group; their existence is dynamic and the actual conversation carried
   out on a channel MUST only be sent to servers which are supporting
   users on a given channel.  Moreover, the message SHALL only be sent
   once to every local link as each server is responsible to fan the
   original message to ensure that it will reach all the recipients.

   The following examples all refer to Figure 1.

   Example 4: Any channel with 1 client in it. Messages to the channel
       go to the server and then nowhere else.

   Example 5: 2 clients in a channel. All messages traverse a path as if
       they were private messages between the two clients outside a
       channel.

   Example 6: Clients 1, 2 and 3 in a channel.  All messages to the
       channel are sent to all clients and only those servers which must
       be traversed by the message if it were a private message to a
       single client.  If client 1 sends a message, it goes back to
       client 2 and then via server B to client 3.

5.2.2 To A Host/Server Mask

   To provide with some mechanism to send messages to a large body of
   related users, host and server mask messages are available.  These
   messages are sent to users whose host or server information match
   that of the mask.  The messages are only sent to locations where
   users are, in a fashion similar to that of channels.

5.2.3 To A List

   The least efficient style of one-to-many conversation is through
   clients talking to a 'list' of targets (client, channel, mask).  How
   this is done is almost self explanatory: the client gives a list of
   destinations to which the message is to be delivered and the server
   breaks it up and dispatches a separate copy of the message to each
   given destination.

   This is not as efficient as using a channel since the destination
   list MAY be broken up and the dispatch sent without checking to make
   sure duplicates aren't sent down each path.

5.3 One-To-All

   The one-to-all type of message is better described as a broadcast
   message, sent to all clients or servers or both.  On a large network
   of users and servers, a single message can result in a lot of traffic
   being sent over the network in an effort to reach all of the desired
   destinations.

   For some class of messages, there is no option but to broadcast it to
   all servers so that the state information held by each server is
   consistent between servers.

5.3.1 Client-to-Client

   There is no class of message which, from a single message, results in
   a message being sent to every other client.

5.3.2 Client-to-Server

   Most of the commands which result in a change of state information
   (such as channel membership, channel mode, user status, etc.) MUST be
   sent to all servers by default, and this distribution SHALL NOT be
   changed by the client.

5.3.3 Server-to-Server

   While most messages between servers are distributed to all 'other'
   servers, this is only required for any message that affects a user,
   channel or server.  Since these are the basic items found in IRC,
   nearly all messages originating from a server are broadcast to all
   other connected servers.

6. Current Problems

   There are a number of recognized problems with this protocol, this
   section only addresses the problems related to the architecture of
   the protocol.

6.1 Scalability

   It is widely recognized that this protocol does not scale
   sufficiently well when used in a large arena.  The main problem comes
   from the requirement that all servers know about all other servers,
   clients and channels and that information regarding them be updated
   as soon as it changes.

6.2 Reliability

   As the only network configuration allowed for IRC servers is that of
   a spanning tree, each link between two servers is an obvious and
   quite serious point of failure.  This particular issue is addressed
   more in detail in "Internet Relay Chat: Server Protocol" [IRC-
   SERVER].

6.3 Network Congestion

   Another problem related to the scalability and reliability issues, as
   well as the spanning tree architecture, is that the protocol and
   architecture for IRC are extremely vulnerable to network congestions.
   This problem is endemic, and should be solved for the next
   generation: if congestion and high traffic volume cause a link
   between two servers to fail, not only this failure generates more
   network traffic, but the reconnection (eventually elsewhere) of two
   servers also generates more traffic.

   In an attempt to minimize the impact of these problems, it is
   strongly RECOMMENDED that servers do not automatically try to
   reconnect too fast, in order to avoid aggravating the situation.

6.4 Privacy

   Besides not scaling well, the fact that servers need to know all
   information about other entities, the issue of privacy is also a
   concern. This is in particular true for channels, as the related
   information is quite a lot more revealing than whether a user is
   online or not.

7. Security Considerations

   Asides from the privacy concerns mentioned in section 6.4 (Privacy),
   security is believed to be irrelevant to this document.

8. Current Support And Availability

        Mailing lists for IRC related discussion:
          General discussion: <EMAIL>
          Protocol development: <EMAIL>

        Software implementations:
          ftp://ftp.irc.org/irc/server
          ftp://ftp.funet.fi/pub/unix/irc
          ftp://coombs.anu.edu.au/pub/irc

        Newsgroup: alt.irc

9. Acknowledgements

   Parts of this document were copied from the RFC 1459 [IRC] which
   first formally documented the IRC Protocol.  It has also benefited
   from many rounds of review and comments.  In particular, the
   following people have made significant contributions to this
   document:

   Matthew Green, Michael Neumayer, Volker Paulsen, Kurt Roeckx, Vesa
   Ruokonen, Magnus Tjernstrom, Stefan Zehl.

10. References

   [KEYWORDS]   Bradner, S., "Key words for use in RFCs to Indicate
                Requirement Levels", BCP 14, RFC 2119, March 1997.

   [IRC]        Oikarinen, J. and D. Reed, "Internet Relay Chat
                Protocol", RFC 1459, May 1993.

   [IRC-CLIENT] Kalt, C., "Internet Relay Chat: Client Protocol", RFC
                2812, April 2000.

   [IRC-SERVER] Kalt, C., "Internet Relay Chat: Server Protocol", RFC
                2813, April 2000.

   [IRC-CHAN]   Kalt, C., "Internet Relay Chat: Channel Management", RFC
                2811, April 2000.

11. Author's Address

   Christophe Kalt
   99 Teaneck Rd, Apt #117
   Ridgefield Park, NJ 07660
   USA

   EMail: <EMAIL>

12.  Full Copyright Statement

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

   This document and translations of it may be copied and furnished to
   others, and derivative works that comment on or otherwise explain it
   or assist in its implementation may be prepared, copied, published
   and distributed, in whole or in part, without restriction of any
   kind, provided that the above copyright notice and this paragraph are
   included on all such copies and derivative works.  However, this
   document itself may not be modified in any way, such as by removing
   the copyright notice or references to the Internet Society or other
   Internet organizations, except as needed for the purpose of
   developing Internet standards in which case the procedures for
   copyrights defined in the Internet Standards process must be
   followed, or as required to translate it into languages other than
   English.

   The limited permissions granted above are perpetual and will not be
   revoked by the Internet Society or its successors or assigns.

   This document and the information contained herein is provided on an
   "AS IS" basis and THE INTERNET SOCIETY AND THE INTERNET ENGINEERING
   TASK FORCE DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING
   BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION
   HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF
   MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

Acknowledgement

   Funding for the RFC Editor function is currently provided by the
   Internet Society.



#### RFC 2811 -  Internet Relay Chat: Channel Management







Network Working Group                                            C. Kalt
Request for Comments: 2811                                    April 2000
Updates: 1459
Category: Informational


                Internet Relay Chat: Channel Management

Status of this Memo

   This memo provides information for the Internet community.  It does
   not specify an Internet standard of any kind.  Distribution of this
   memo is unlimited.

Copyright Notice

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

Abstract

   One of the most notable characteristics of the IRC (Internet Relay
   Chat) protocol is to allow for users to be grouped in forums, called
   channels, providing a mean for multiple users to communicate
   together.

   There was originally a unique type of channels, but with the years,
   new types appeared either as a response to a need, or for
   experimental purposes.

   This document specifies how channels, their characteristics and
   properties are managed by IRC servers.

Table of Contents

   1.  Introduction ...............................................   2
   2.  Channel Characteristics ....................................   3
      2.1  Namespace ..............................................   3
      2.2  Channel Scope ..........................................   3
      2.3  Channel Properties .....................................   4
      2.4  Privileged Channel Members .............................   4
         2.4.1  Channel Operators .................................   5
         2.4.2  Channel Creator ...................................   5
   3.  Channel lifetime ...........................................   5
      3.1  Standard channels ......................................   5
      3.2  Safe Channels ..........................................   6
   4.  Channel Modes ..............................................   7
      4.1  Member Status ..........................................   7
         4.1.1  "Channel Creator" Status ..........................   7



Kalt                         Informational                      [Page 1]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


         4.1.2  Channel Operator Status ...........................   8
         4.1.3  Voice Privilege ...................................   8
      4.2  Channel Flags ..........................................   8
         4.2.1  Anonymous Flag ....................................   8
         4.2.2  Invite Only Flag ..................................   8
         4.2.3  Moderated Channel Flag ............................   9
         4.2.4  No Messages To Channel From Clients On The Outside    9
         4.2.5  Quiet Channel .....................................   9
         4.2.6  Private and Secret Channels .......................   9
         4.2.7  Server Reop Flag ..................................  10
         4.2.8  Topic .............................................  10
         4.2.9  User Limit ........................................  10
         4.2.10  Channel Key ......................................  10
      4.3  Channel Access Control .................................  10
         4.3.1  Channel Ban and Exception .........................  11
         4.3.2  Channel Invitation ................................  11
   5.  Current Implementations ....................................  11
      5.1  Tracking Recently Used Channels ........................  11
      5.2  Safe Channels ..........................................  12
         5.2.1  Channel Identifier ................................  12
         5.2.2  Channel Delay .....................................  12
         5.2.3  Abuse Window ......................................  13
         5.2.4  Preserving Sanity In The Name Space ...............  13
         5.2.5  Server Reop Mechanism .............................  13
   6.  Current problems ...........................................  14
      6.1  Labels .................................................  14
         6.1.1  Channel Delay .....................................  14
         6.1.2  Safe Channels .....................................  15
      6.2  Mode Propagation Delays ................................  15
      6.3  Collisions And Channel Modes ...........................  15
      6.4  Resource Exhaustion ....................................  16
   7.  Security Considerations ....................................  16
      7.1  Access Control .........................................  16
      7.2  Channel Privacy ........................................  16
      7.3 Anonymity ...............................................  17
   8.  Current support and availability ...........................  17
   9.  Acknowledgements ...........................................  17
   10. References ................................................   18
   11. Author's Address ..........................................   18
   12. Full Copyright Statement ...................................  19

1. Introduction

   This document defines in detail on how channels are managed by the
   IRC servers and will be mostly useful to people working on
   implementing an IRC server.





Kalt                         Informational                      [Page 2]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   While the concepts defined here are an important part of IRC, they
   remain non essential for implementing clients.  While the trend seems
   to be towards more and more complex and "intelligent" clients which
   are able to take advantage of knowing the internal workings of
   channels to provide the users with a more friendly interface, simple
   clients can be implemented without reading this document.

   Many of the concepts defined here were designed with the IRC
   architecture [IRC-ARCH] in mind and mostly make sense in this
   context.  However, many others could be applied to other
   architectures in order to provide forums for a conferencing system.

   Finally, it is to be noted that IRC users may find some of the
   following sections of interest, in particular sections 2 (Channel
   Characteristics) and 4 (Channel Modes).

2. Channel Characteristics

   A channel is a named group of one or more users which will all
   receive messages addressed to that channel.  A channel is
   characterized by its name, properties and current members.

2.1 Namespace

   Channels names are strings (beginning with a '&', '#', '+' or '!'
   character) of length up to fifty (50) characters.  Channel names are
   case insensitive.

   Apart from the the requirement that the first character being either
   '&', '#', '+' or '!' (hereafter called "channel prefix"). The only
   restriction on a channel name is that it SHALL NOT contain any spaces
   (' '), a control G (^G or ASCII 7), a comma (',' which is used as a
   list item separator by the protocol).  Also, a colon (':') is used as
   a delimiter for the channel mask.  The exact syntax of a channel name
   is defined in "IRC Server Protocol" [IRC-SERVER].

   The use of different prefixes effectively creates four (4) distinct
   namespaces for channel names.  This is important because of the
   protocol limitations regarding namespaces (in general).  See section
   6.1 (Labels) for more details on these limitations.

2.2 Channel Scope

   A channel entity is known by one or more servers on the IRC network.
   A user can only become member of a channel known by the server to
   which the user is directly connected.  The list of servers which know





Kalt                         Informational                      [Page 3]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   of the existence of a particular channel MUST be a contiguous part of
   the IRC network, in order for the messages addressed to the channel
   to be sent to all the channel members.

   Channels with '&' as prefix are local to the server where they are
   created.

   Other channels are known to one (1) or more servers that are
   connected to the network, depending on the channel mask:

      If there is no channel mask, then the channel is known to all
      the servers.

      If there is a channel mask, then the channel MUST only be known
      to servers which has a local user on the channel, and to its
      neighbours if the mask matches both the local and neighbouring
      server names.  Since other servers have absolutely no knowledge of
      the existence of such a channel, the area formed by the servers
      having a name matching the mask has to be contiguous for the
      channel to be known by all these servers.  Channel masks are best
      used in conjunction with server hostmasking [IRC-SERVER].

2.3 Channel Properties

   Each channel has its own properties, which are defined by channel
   modes.  Channel modes can be manipulated by the channel members.  The
   modes affect the way servers manage the channels.

   Channels with '+' as prefix do not support channel modes.  This means
   that all the modes are unset, with the exception of the 't' channel
   flag which is set.

2.4 Privileged Channel Members

   In order for the channel members to keep some control over a channel,
   and some kind of sanity, some channel members are privileged.  Only
   these members are allowed to perform the following actions on the
   channel:

        INVITE  - Invite a client to an invite-only channel (mode +i)
        KICK    - Eject a client from the channel
        MODE    - Change the channel's mode, as well as
                  members' privileges
        PRIVMSG - Sending messages to the channel (mode +n, +m, +v)
        TOPIC   - Change the channel topic in a mode +t channel






Kalt                         Informational                      [Page 4]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


2.4.1 Channel Operators

   The channel operators (also referred to as a "chop" or "chanop") on a
   given channel are considered to 'own' that channel.  Ownership of a
   channel is shared among channel operators.

   Channel operators are identified by the '@' symbol next to their
   nickname whenever it is associated with a channel (i.e., replies to
   the NAMES, WHO and WHOIS commands).

   Since channels starting with the character '+' as prefix do not
   support channel modes, no member can therefore have the status of
   channel operator.

2.4.2 Channel Creator

   A user who creates a channel with the character '!' as prefix is
   identified as the "channel creator".  Upon creation of the channel,
   this user is also given channel operator status.

   In recognition of this status, the channel creators are endowed with
   the ability to toggle certain modes of the channel which channel
   operators may not manipulate.

   A "channel creator" can be distinguished from a channel operator by
   issuing the proper MODE command.  See the "IRC Client Protocol"
   [IRC-CLIENT] for more information on this topic.

3. Channel lifetime

   In regard to the lifetime of a channel, there are typically two
   groups of channels: standard channels which prefix is either '&', '#'
   or '+', and "safe channels" which prefix is '!'.

3.1 Standard channels

   These channels are created implicitly when the first user joins it,
   and cease to exist when the last user leaves it.  While the channel
   exists, any client can reference the channel using the name of the
   channel.

   The user creating a channel automatically becomes channel operator
   with the notable exception of channels which name is prefixed by the
   character '+', see section 4 (Channel modes).  See section 2.4.1
   (Channel Operators) for more details on this title.






Kalt                         Informational                      [Page 5]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   In order to avoid the creation of duplicate channels (typically when
   the IRC network becomes disjoint because of a split between two
   servers), channel names SHOULD NOT be allowed to be reused by a user
   if a channel operator (See Section 2.4.1 (Channel Operators)) has
   recently left the channel because of a network split.  If this
   happens, the channel name is temporarily unavailable.  The duration
   while a channel remains unavailable should be tuned on a per IRC
   network basis.  It is important to note that this prevents local
   users from creating a channel using the same name, but does not
   prevent the channel to be recreated by a remote user. The latter
   typically happens when the IRC network rejoins.  Obviously, this
   mechanism only makes sense for channels which name begins with the
   character '#', but MAY be used for channels which name begins with
   the character '+'.  This mechanism is commonly known as "Channel
   Delay".

3.2 Safe Channels

   Unlike other channels, "safe channels" are not implicitly created.  A
   user wishing to create such a channel MUST request the creation by
   sending a special JOIN command to the server in which the channel
   identifier (then unknown) is replaced by the character '!'.  The
   creation process for this type of channel is strictly controlled.
   The user only chooses part of the channel name (known as the channel
   "short name"), the server automatically prepends the user provided
   name with a channel identifier consisting of five (5) characters.
   The channel name resulting from the combination of these two elements
   is unique, making the channel safe from abuses based on network
   splits.

   The user who creates such a channel automatically becomes "channel
   creator".  See section 2.4.2 (Channel Creator) for more details on
   this title.

   A server MUST NOT allow the creation of a new channel if another
   channel with the same short name exists; or if another channel with
   the same short name existed recently AND any of its member(s) left
   because of a network split.  Such channel ceases to exist after last
   user leaves AND no other member recently left the channel because of
   a network split.

   Unlike the mechanism described in section 5.2.2 (Channel Delay), in
   this case, channel names do not become unavailable: these channels
   may continue to exist after the last user left.  Only the user
   creating the channel becomes "channel creator", users joining an
   existing empty channel do not automatically become "channel creator"
   nor "channel operator".




Kalt                         Informational                      [Page 6]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   To ensure the uniqueness of the channel names, the channel identifier
   created by the server MUST follow specific rules.  For more details
   on this, see section 5.2.1 (Channel Identifier).

4. Channel Modes

   The various modes available for channels are as follows:

        O - give "channel creator" status;
        o - give/take channel operator privilege;
        v - give/take the voice privilege;

        a - toggle the anonymous channel flag;
        i - toggle the invite-only channel flag;
        m - toggle the moderated channel;
        n - toggle the no messages to channel from clients on the
            outside;
        q - toggle the quiet channel flag;
        p - toggle the private channel flag;
        s - toggle the secret channel flag;
        r - toggle the server reop channel flag;
        t - toggle the topic settable by channel operator only flag;

        k - set/remove the channel key (password);
        l - set/remove the user limit to channel;

        b - set/remove ban mask to keep users out;
        e - set/remove an exception mask to override a ban mask;
        I - set/remove an invitation mask to automatically override
            the invite-only flag;

   Unless mentioned otherwise below, all these modes can be manipulated
   by "channel operators" by using the MODE command defined in "IRC
   Client Protocol" [IRC-CLIENT].

4.1 Member Status

   The modes in this category take a channel member nickname as argument
   and affect the privileges given to this user.

4.1.1 "Channel Creator" Status

   The mode 'O' is only used in conjunction with "safe channels" and
   SHALL NOT be manipulated by users.  Servers use it to give the user
   creating the channel the status of "channel creator".






Kalt                         Informational                      [Page 7]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


4.1.2 Channel Operator Status

   The mode 'o' is used to toggle the operator status of a channel
   member.

4.1.3 Voice Privilege

   The mode 'v' is used to give and take voice privilege to/from a
   channel member.  Users with this privilege can talk on moderated
   channels.  (See section 4.2.3 (Moderated Channel Flag).

4.2 Channel Flags

   The modes in this category are used to define properties which
   affects how channels operate.

4.2.1 Anonymous Flag

   The channel flag 'a' defines an anonymous channel.  This means that
   when a message sent to the channel is sent by the server to users,
   and the origin is a user, then it MUST be masked.  To mask the
   message, the origin is changed to "anonymous!anonymous@anonymous."
   (e.g., a user with the nickname "anonymous", the username "anonymous"
   and from a host called "anonymous.").  Because of this, servers MUST
   forbid users from using the nickname "anonymous".  Servers MUST also
   NOT send QUIT messages for users leaving such channels to the other
   channel members but generate a PART message instead.

   On channels with the character '&' as prefix, this flag MAY be
   toggled by channel operators, but on channels with the character '!'
   as prefix, this flag can be set (but SHALL NOT be unset) by the
   "channel creator" only.  This flag MUST NOT be made available on
   other types of channels.

   Replies to the WHOIS, WHO and NAMES commands MUST NOT reveal the
   presence of other users on channels for which the anonymous flag is
   set.

4.2.2 Invite Only Flag

   When the channel flag 'i' is set, new members are only accepted if
   their mask matches Invite-list (See section 4.3.2) or they have been
   invited by a channel operator.  This flag also restricts the usage of
   the INVITE command (See "IRC Client Protocol" [IRC-CLIENT]) to
   channel operators.






Kalt                         Informational                      [Page 8]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


4.2.3 Moderated Channel Flag

   The channel flag 'm' is used to control who may speak on a channel.
   When it is set, only channel operators, and members who have been
   given the voice privilege may send messages to the channel.

      This flag only affects users.

4.2.4 No Messages To Channel From Clients On The Outside

   When the channel flag 'n' is set, only channel members MAY send
   messages to the channel.

      This flag only affects users.

4.2.5 Quiet Channel

   The channel flag 'q' is for use by servers only.  When set, it
   restricts the type of data sent to users about the channel
   operations: other user joins, parts and nick changes are not sent.
   From a user's point of view, the channel contains only one user.

   This is typically used to create special local channels on which the
   server sends notices related to its operations.  This was used as a
   more efficient and flexible way to replace the user mode 's' defined
   in RFC 1459 [IRC].

4.2.6 Private and Secret Channels

   The channel flag 'p' is used to mark a channel "private" and the
   channel flag 's' to mark a channel "secret".  Both properties are
   similar and conceal the existence of the channel from other users.

   This means that there is no way of getting this channel's name from
   the server without being a member.  In other words, these channels
   MUST be omitted from replies to queries like the WHOIS command.

   When a channel is "secret", in addition to the restriction above, the
   server will act as if the channel does not exist for queries like the
   TOPIC, LIST, NAMES commands.  Note that there is one exception to
   this rule: servers will correctly reply to the MODE command.
   Finally, secret channels are not accounted for in the reply to the
   LUSERS command (See "Internet Relay Chat: Client Protocol" [IRC-
   CLIENT]) when the <mask> parameter is specified.







Kalt                         Informational                      [Page 9]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   The channel flags 'p' and 's' MUST NOT both be set at the same time.
   If a MODE message originating from a server sets the flag 'p' and the
   flag 's' is already set for the channel, the change is silently
   ignored.  This should only happen during a split healing phase
   (mentioned in the "IRC Server Protocol" document [IRC-SERVER]).

4.2.7 Server Reop Flag

   The channel flag 'r' is only available on channels which name begins
   with the character '!' and MAY only be toggled by the "channel
   creator".

   This flag is used to prevent a channel from having no channel
   operator for an extended period of time.  When this flag is set, any
   channel that has lost all its channel operators for longer than the
   "reop delay" period triggers a mechanism in servers to reop some or
   all of the channel inhabitants.  This mechanism is described more in
   detail in section 5.2.4 (Channel Reop Mechanism).

4.2.8 Topic

   The channel flag 't' is used to restrict the usage of the TOPIC
   command to channel operators.

4.2.9 User Limit

   A user limit may be set on channels by using the channel flag 'l'.
   When the limit is reached, servers MUST forbid their local users to
   join the channel.

   The value of the limit MUST only be made available to the channel
   members in the reply sent by the server to a MODE query.

4.2.10 Channel Key

   When a channel key is set (by using the mode 'k'), servers MUST
   reject their local users request to join the channel unless this key
   is given.

   The channel key MUST only be made visible to the channel members in
   the reply sent by the server to a MODE query.

4.3 Channel Access Control

   The last category of modes is used to control access to the channel,
   they take a mask as argument.





Kalt                         Informational                     [Page 10]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   In order to reduce the size of the global database for control access
   modes set for channels, servers MAY put a maximum limit on the number
   of such modes set for a particular channel.  If such restriction is
   imposed, it MUST only affect user requests.  The limit SHOULD be
   homogeneous on a per IRC network basis.

4.3.1 Channel Ban and Exception

   When a user requests to join a channel, his local server checks if
   the user's address matches any of the ban masks set for the channel.
   If a match is found, the user request is denied unless the address
   also matches an exception mask set for the channel.

   Servers MUST NOT allow a channel member who is banned from the
   channel to speak on the channel, unless this member is a channel
   operator or has voice privilege. (See Section 4.1.3 (Voice
   Privilege)).

   A user who is banned from a channel and who carries an invitation
   sent by a channel operator is allowed to join the channel.

4.3.2 Channel Invitation

   For channels which have the invite-only flag set (See Section 4.2.2
   (Invite Only Flag)), users whose address matches an invitation mask
   set for the channel are allowed to join the channel without any
   invitation.

5. Current Implementations

   The only current implementation of these rules as part of the IRC
   protocol is the IRC server, version 2.10.

   The rest of this section deals with issues that are mostly of
   importance to those who wish to implement a server but some parts may
   also be of interest for client writers.

5.1 Tracking Recently Used Channels

   This mechanism is commonly known as "Channel Delay" and generally
   only applies to channels which names is prefixed with the character
   '#' (See Section 3.1 "Standard channels").

   When a network split occurs, servers SHOULD keep track of which
   channels lost a "channel operator" as the result of the break.  These
   channels are then in a special state which lasts for a certain period
   of time.  In this particular state, the channels cannot cease to




Kalt                         Informational                     [Page 11]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   exist.  If all the channel members leave the channel, the channel
   becomes unavailable: the server local clients cannot join the channel
   as long as it is empty.

   Once a channel is unavailable, it will become available again either
   because a remote user has joined the channel (most likely because the
   network is healing), or because the delay period has expired (in
   which case the channel ceases to exist and may be re-created).

   The duration for which a channel death is delayed SHOULD be set
   considering many factors among which are the size (user wise) of the
   IRC network, and the usual duration of network splits.  It SHOULD be
   uniform on all servers for a given IRC network.

5.2 Safe Channels

   This document introduces the notion of "safe channels".  These
   channels have a name prefixed with the character '!' and great effort
   is made to avoid collisions in this name space.  Collisions are not
   impossible, however they are very unlikely.

5.2.1 Channel Identifier

   The channel identifier is a function of the time.  The current time
   (as defined under UNIX by the number of seconds elapsed since
   00:00:00 GMT, January 1, 1970) is converted in a string of five (5)
   characters using the following base:
   "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890" (each character has a decimal
   value starting from 0 for 'A' to 35 for '0').

   The channel identifier therefore has a periodicity of 36^5 seconds
   (about 700 days).

5.2.2 Channel Delay

   These channels MUST be subject to the "channel delay" mechanism
   described in section 5.1 (Channel Delay).  However, the mechanism is
   slightly adapted to fit better.

   Servers MUST keep track of all such channels which lose members as
   the result of a network split, no matter whether the user is a
   "channel operator" or not.

   However, these channels do NOT ever become unavailable, it is always
   possible to join them even when they are empty.






Kalt                         Informational                     [Page 12]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


5.2.3 Abuse Window

   Because the periodicity is so long, attacks on a particular channel
   (name) may only occur once in a very long while.  However, with luck
   and patience, it is still possible for a user to cause a channel
   collision.  In order to avoid this, servers MUST "look in the future"
   and keep a list of channel names which identifier is about to be used
   (in the coming few days for example). Such list should remain small,
   not be a burden for servers to maintain and be used to avoid channel
   collisions by preventing the re-creation of such channel for a longer
   period of time than channel delay does.

   Eventually a server MAY choose to extend this procedure to forbid
   creation of channels with the same shortname only (then ignoring the
   channel identifier).

5.2.4 Preserving Sanity In The Name Space

   The combination of the mechanisms described in sections 5.2.2 and
   5.2.3 makes it quite difficult for a user to create a channel
   collision. However, another type of abuse consists of creating many
   channels having the same shortname, but different identifiers.  To
   prevent this from happening, servers MUST forbid the creation of a
   new channel which has the same shortname of a channel currently
   existing.

5.2.5 Server Reop Mechanism

   When a channel has been opless for longer than the "reop delay"
   period and has the channel flag 'r' set (See Section 4.2.7 (Server
   Reop Flag)), IRC servers are responsible for giving the channel
   operator status randomly to some of the members.

   The exact logic used for this mechanism by the current implementation
   is described below.  Servers MAY use a different logic, but that it
   is strongly RECOMMENDED that all servers use the same logic on a
   particular IRC network to maintain coherence as well as fairness.
   For the same reason, the "reop delay" SHOULD be uniform on all
   servers for a given IRC network.  As for the "channel delay", the
   value of the "reop delay" SHOULD be set considering many factors
   among which are the size (user wise) of the IRC network, and the
   usual duration of network splits.

   a) the reop mechanism is triggered after a random time following the
      expiration of the "reop delay".  This should limit the eventuality
      of the mechanism being triggered at the same time (for the same
      channel) on two separate servers.




Kalt                         Informational                     [Page 13]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   b) If the channel is small (five (5) users or less), and the "channel
      delay" for this channel has expired,
        Then reop all channel members if at least one member is local to
        the server.

   c) If the channel is small (five (5) users or less), and the "channel
      delay" for this channel has expired, and the "reop delay" has
      expired for longer than its value,
        Then reop all channel members.

   d) For other cases, reop at most one member on the channel, based on
      some method build into the server. If you don't reop a member, the
      method should be such that another server will probably op
      someone. The method SHOULD be the same over the whole network. A
      good heuristic could be just random reop.
      (The current implementation actually tries to choose a member
      local to the server who has not been idle for too long, eventually
      postponing action, therefore letting other servers have a chance
      to find a "not too idle" member.  This is over complicated due to
      the fact that servers only know the "idle" time of their local
      users)

6. Current problems

   There are a number of recognized problems with the way IRC channels
   are managed.  Some of these can be directly attributed to the rules
   defined in this document, while others are the result of the
   underlying "IRC Server Protocol" [IRC-SERVER].  Although derived from
   RFC 1459 [IRC], this document introduces several novelties in an
   attempt to solve some of the known problems.

6.1 Labels

   This document defines one of the many labels used by the IRC
   protocol.  Although there are several distinct namespaces (based on
   the channel name prefix), duplicates inside each of these are not
   allowed.  Currently, it is possible for users on different servers to
   pick the label which may result in collisions (with the exception of
   channels known to only one server where they can be averted).

6.1.1 Channel Delay

   The channel delay mechanism described in section 5.1 (Tracking
   Recently Used Channels) and used for channels prefixed with the
   character '#' is a simple attempt at preventing collisions from
   happening.  Experience has shown that, under normal circumstances, it





Kalt                         Informational                     [Page 14]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   is very efficient; however, it obviously has severe limitations
   keeping it from being an adequate solution to the problem discussed
   here.

6.1.2 Safe Channels

   "Safe channels" described in section 3.2 (Safe Channels) are a better
   way to prevent collisions from happening as it prevents users from
   having total control over the label they choose.  The obvious
   drawback for such labels is that they are not user friendly.
   However, it is fairly trivial for a client program to improve on
   this.

6.2 Mode Propagation Delays

   Because of network delays induced by the network, and because each
   server on the path is REQUIRED to check the validity of mode changes
   (e.g., user exists and has the right privileges), it is not unusual
   for a MODE message to only affect part of the network, often creating
   a discrepancy between servers on the current state of a channel.

   While this may seem easy to fix (by having only the original server
   check the validity of mode changes), it was decided not to do so for
   various reasons.  One concern is that servers cannot trust each
   other, and that a misbehaving servers can easily be detected.  This
   way of doing so also stops wave effects on channels which are out of
   synch when mode changes are issued from different directions.

6.3 Collisions And Channel Modes

   The "Internet Relay Chat: Server Protocol" document [IRC-SERVER]
   describes how channel data is exchanged when two servers connect to
   each other.  Channel collisions (either legitimate or not) are
   treated as inclusive events, meaning that the resulting channel has
   for members all the users who are members of the channel on either
   server prior to the connection.

   Similarly, each server sends the channel modes to the other one.
   Therefore, each server also receives these channel modes.  There are
   three types of modes for a given channel: flags, masks, and data.
   The first two types are easy to deal with as they are either set or
   unset.  If such a mode is set on one server, it MUST be set on the
   other server as a result of the connection.








Kalt                         Informational                     [Page 15]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


   As topics are not sent as part of this exchange, they are not a
   problem.  However, channel modes 'l' and 'k' are exchanged, and if
   they are set on both servers prior to the connection, there is no
   mechanism to decide which of the two values takes precedence.  It is
   left up to the users to fix the resulting discrepancy.

6.4 Resource Exhaustion

   The mode based on masks defined in section 4.3 make the IRC servers
   (and network) vulnerable to a simple abuse of the system: a single
   channel operator can set as many different masks as possible on a
   particular channel.  This can easily cause the server to waste
   memory, as well as network bandwidth (since the info is propagated to
   other servers).  For this reason it is RECOMMENDED that a limit be
   put on the number of such masks per channels as mentioned in section
   4.3.

   Moreover, more complex mechanisms MAY be used to avoid having
   redundant masks set for the same channel.

7. Security Considerations

7.1 Access Control

   One of the main ways to control access to a channel is to use masks
   which are based on the username and hostname of the user connections.
   This mechanism can only be efficient and safe if the IRC servers have
   an accurate way of authenticating user connections, and if users
   cannot easily get around it.  While it is in theory possible to
   implement such a strict authentication mechanism, most IRC networks
   (especially public networks) do not have anything like this in place
   and provide little guaranty about the accuracy of the username and
   hostname for a particular client connection.

   Another way to control access is to use a channel key, but since this
   key is sent in plaintext, it is vulnerable to traditional man in the
   middle attacks.

7.2 Channel Privacy

   Because channel collisions are treated as inclusive events (See
   Section 6.3), it is possible for users to join a channel overriding
   its access control settings.  This method has long been used by
   individuals to "take over" channels by "illegitimately" gaining
   channel operator status on the channel.  The same method can be used
   to find out the exact list of members of a channel, as well as to
   eventually receive some of the messages sent to the channel.




Kalt                         Informational                     [Page 16]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


7.3 Anonymity

   The anonymous channel flag (See Section 4.2.1) can be used to render
   all users on such channel "anonymous" by presenting all messages to
   the channel as originating from a pseudo user which nickname is
   "anonymous".  This is done at the client-server level, and no
   anonymity is provided at the server-server level.

   It should be obvious to readers, that the level of anonymity offered
   is quite poor and insecure, and that clients SHOULD display strong
   warnings for users joining such channels.

8. Current support and availability

     Mailing lists for IRC related discussion:
       General discussion: <EMAIL>
       Protocol development: <EMAIL>

     Software implementations:
       ftp://ftp.irc.org/irc/server
       ftp://ftp.funet.fi/pub/unix/irc
       ftp://coombs.anu.edu.au/pub/irc

     Newsgroup: alt.irc

9. Acknowledgements

   Parts of this document were copied from the RFC 1459 [IRC] which
   first formally documented the IRC Protocol.  It has also benefited
   from many rounds of review and comments.  In particular, the
   following people have made significant contributions to this
   document:

   Matthew Green, Michael Neumayer, Volker Paulsen, Kurt Roeckx, Vesa
   Ruokonen, Magnus Tjernstrom, Stefan Zehl.
















Kalt                         Informational                     [Page 17]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


10. References

   [KEYWORDS]   Bradner, S., "Key words for use in RFCs to Indicate
                Requirement Levels", BCP 14, RFC 2119, March 1997.

   [IRC]        Oikarinen, J. and D. Reed, "Internet Relay Chat
                Protocol", RFC 1459, May 1993.

   [IRC-ARCH]   Kalt, C., "Internet Relay Chat: Architecture", RFC 2810,
                April 2000.

   [IRC-CLIENT] Kalt, C., "Internet Relay Chat: Client Protocol", RFC
                2812, April 2000.

   [IRC-SERVER] Kalt, C., "Internet Relay Chat: Server Protocol", RFC
                2813, April 2000.

11. Author's Address

   Christophe Kalt
   99 Teaneck Rd, Apt #117
   Ridgefield Park, NJ 07660
   USA

   EMail: <EMAIL>


























Kalt                         Informational                     [Page 18]

RFC 2811        Internet Relay Chat: Channel Management       April 2000


12.  Full Copyright Statement

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

   This document and translations of it may be copied and furnished to
   others, and derivative works that comment on or otherwise explain it
   or assist in its implementation may be prepared, copied, published
   and distributed, in whole or in part, without restriction of any
   kind, provided that the above copyright notice and this paragraph are
   included on all such copies and derivative works.  However, this
   document itself may not be modified in any way, such as by removing
   the copyright notice or references to the Internet Society or other
   Internet organizations, except as needed for the purpose of
   developing Internet standards in which case the procedures for
   copyrights defined in the Internet Standards process must be
   followed, or as required to translate it into languages other than
   English.

   The limited permissions granted above are perpetual and will not be
   revoked by the Internet Society or its successors or assigns.

   This document and the information contained herein is provided on an
   "AS IS" basis and THE INTERNET SOCIETY AND THE INTERNET ENGINEERING
   TASK FORCE DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING
   BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION
   HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF
   MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

Acknowledgement

   Funding for the RFC Editor function is currently provided by the
   Internet Society.



#### RFC 2812 - Internet Relay Chat: Client Protocol

Network Working Group                                            C. Kalt
Request for Comments: 2812                                    April 2000
Updates: 1459
Category: Informational


                  Internet Relay Chat: Client Protocol

Status of this Memo

   This memo provides information for the Internet community.  It does
   not specify an Internet standard of any kind.  Distribution of this
   memo is unlimited.

Copyright Notice

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

IESG NOTE:

   The IRC protocol itself enables several possibilities of transferring
   data between clients, and just like with other transfer mechanisms
   like email, the receiver of the data has to be careful about how the
   data is handled. For more information on security issues with the IRC
   protocol, see for example http://www.irchelp.org/irchelp/security/.

Abstract

   The IRC (Internet Relay Chat) protocol is for use with text based
   conferencing; the simplest client being any socket program capable of
   connecting to the server.

   This document defines the Client Protocol, and assumes that the
   reader is familiar with the IRC Architecture [IRC-ARCH].

Table of Contents

   1.  Labels .....................................................   3
      1.1  Servers ................................................   3
      1.2  Clients ................................................   3
         1.2.1  Users .............................................   4
            1.2.1.1  Operators ....................................   4
         1.2.2  Services ..........................................   4
      1.3  Channels ...............................................   4
   2.  The IRC Client Specification ...............................   5
      2.1  Overview ...............................................   5
      2.2  Character codes ........................................   5
      2.3  Messages ...............................................   5

         2.3.1  Message format in Augmented BNF ...................   6
      2.4  Numeric replies ........................................   8
      2.5  Wildcard expressions ...................................   9
   3.  Message Details ............................................   9
      3.1  Connection Registration ................................  10
         3.1.1  Password message ..................................  10
         3.1.2  Nick message ......................................  10
         3.1.3  User message ......................................  11
         3.1.4  Oper message ......................................  12
         3.1.5  User mode message .................................  12
         3.1.6  Service message ...................................  13
         3.1.7  Quit ..............................................  14
         3.1.8  Squit .............................................  15
      3.2  Channel operations .....................................  15
         3.2.1  Join message ......................................  16
         3.2.2  Part message ......................................  17
         3.2.3  Channel mode message ..............................  18
         3.2.4  Topic message .....................................  19
         3.2.5  Names message .....................................  20
         3.2.6  List message ......................................  21
         3.2.7  Invite message ....................................  21
         3.2.8  Kick command ......................................  22
      3.3  Sending messages .......................................  23
         3.3.1  Private messages ..................................  23
         3.3.2  Notice ............................................  24
      3.4  Server queries and commands ............................  25
         3.4.1  Motd message ......................................  25
         3.4.2  Lusers message ....................................  25
         3.4.3  Version message ...................................  26
         3.4.4  Stats message .....................................  26
         3.4.5  Links message .....................................  27
         3.4.6  Time message ......................................  28
         3.4.7  Connect message ...................................  28
         3.4.8  Trace message .....................................  29
         3.4.9  Admin command .....................................  30
         3.4.10 Info command ......................................  31
      3.5  Service Query and Commands .............................  31
         3.5.1  Servlist message ..................................  31
         3.5.2  Squery ............................................  32
      3.6  User based queries .....................................  32
         3.6.1  Who query .........................................  32
         3.6.2  Whois query .......................................  33
         3.6.3  Whowas ............................................  34
      3.7  Miscellaneous messages .................................  34
         3.7.1  Kill message ......................................  35
         3.7.2  Ping message ......................................  36
         3.7.3  Pong message ......................................  37
         3.7.4  Error .............................................  37

   4.  Optional features ..........................................  38
      4.1  Away ...................................................  38
      4.2  Rehash message .........................................  39
      4.3  Die message ............................................  39
      4.4  Restart message ........................................  40
      4.5  Summon message .........................................  40
      4.6  Users ..................................................  41
      4.7  Operwall message .......................................  41
      4.8  Userhost message .......................................  42
      4.9  Ison message ...........................................  42
   5.  Replies ....................................................  43
      5.1  Command responses ......................................  43
      5.2  Error Replies ..........................................  53
      5.3  Reserved numerics ......................................  59
   6.  Current implementations ....................................  60
   7.  Current problems ...........................................  60
      7.1  Nicknames ..............................................  60
      7.2  Limitation of wildcards ................................  61
      7.3  Security considerations ................................  61
   8.  Current support and availability ...........................  61
   9.  Acknowledgements ...........................................  61
   10.  References ................................................  62
   11.  Author's Address ..........................................  62
   12.  Full Copyright Statement ..................................  63

1. Labels

   This section defines the identifiers used for the various components
   of the IRC protocol.

1.1 Servers

   Servers are uniquely identified by their name, which has a maximum
   length of sixty three (63) characters.  See the protocol grammar
   rules (section 2.3.1) for what may and may not be used in a server
   name.

1.2 Clients

   For each client all servers MUST have the following information: a
   netwide unique identifier (whose format depends on the type of
   client) and the server which introduced the client.

1.2.1 Users

   Each user is distinguished from other users by a unique nickname
   having a maximum length of nine (9) characters.  See the protocol
   grammar rules (section 2.3.1) for what may and may not be used in a
   nickname.

   While the maximum length is limited to nine characters, clients
   SHOULD accept longer strings as they may become used in future
   evolutions of the protocol.

1.2.1.1 Operators

   To allow a reasonable amount of order to be kept within the IRC
   network, a special class of users (operators) is allowed to perform
   general maintenance functions on the network.  Although the powers
   granted to an operator can be considered as 'dangerous', they are
   nonetheless often necessary.  Operators SHOULD be able to perform
   basic network tasks such as disconnecting and reconnecting servers as
   needed.  In recognition of this need, the protocol discussed herein
   provides for operators only to be able to perform such functions.
   See sections 3.1.8 (SQUIT) and 3.4.7 (CONNECT).

   A more controversial power of operators is the ability to remove a
   user from the connected network by 'force', i.e., operators are able
   to close the connection between any client and server.  The
   justification for this is very delicate since its abuse is both
   destructive and annoying, and its benefits close to inexistent.  For
   further details on this type of action, see section 3.7.1 (KILL).

1.2.2 Services

   Each service is distinguished from other services by a service name
   composed of a nickname and a server name.  As for users, the nickname
   has a maximum length of nine (9) characters.  See the protocol
   grammar rules (section 2.3.1) for what may and may not be used in a
   nickname.

1.3 Channels

   Channels names are strings (beginning with a '&', '#', '+' or '!'
   character) of length up to fifty (50) characters.  Apart from the
   requirement that the first character is either '&', '#', '+' or '!',
   the only restriction on a channel name is that it SHALL NOT contain
   any spaces (' '), a control G (^G or ASCII 7), a comma (',').  Space
   is used as parameter separator and command is used as a list item
   separator by the protocol).  A colon (':') can also be used as a
   delimiter for the channel mask.  Channel names are case insensitive.

   See the protocol grammar rules (section 2.3.1) for the exact syntax
   of a channel name.

   Each prefix characterizes a different channel type.  The definition
   of the channel types is not relevant to the client-server protocol
   and thus it is beyond the scope of this document.  More details can
   be found in "Internet Relay Chat: Channel Management" [IRC-CHAN].

2. The IRC Client Specification

2.1 Overview

   The protocol as described herein is for use only with client to
   server connections when the client registers as a user.

2.2 Character codes

   No specific character set is specified. The protocol is based on a
   set of codes which are composed of eight (8) bits, making up an
   octet.  Each message may be composed of any number of these octets;
   however, some octet values are used for control codes, which act as
   message delimiters.

   Regardless of being an 8-bit protocol, the delimiters and keywords
   are such that protocol is mostly usable from US-ASCII terminal and a
   telnet connection.

   Because of IRC's Scandinavian origin, the characters {}|^ are
   considered to be the lower case equivalents of the characters []\~,
   respectively. This is a critical issue when determining the
   equivalence of two nicknames or channel names.

2.3 Messages

   Servers and clients send each other messages, which may or may not
   generate a reply.  If the message contains a valid command, as
   described in later sections, the client should expect a reply as
   specified but it is not advised to wait forever for the reply; client
   to server and server to server communication is essentially
   asynchronous by nature.

   Each IRC message may consist of up to three main parts: the prefix
   (OPTIONAL), the command, and the command parameters (maximum of
   fifteen (15)).  The prefix, command, and all parameters are separated
   by one ASCII space character (0x20) each.

      The presence of a prefix is indicated with a single leading ASCII Expand
   colon character (':', 0x3a), which MUST be the first character of
   the message itself.
  There MUST be NO gap (whitespace) between the colon
   and the prefix.  The prefix is used by servers to indicate the true
   origin of the message.  If the prefix is missing from the message, it
   is assumed to have originated from the connection from which it was
   received from.  Clients SHOULD NOT use a prefix when sending a
   message; if they use one, the only valid prefix is the registered
   nickname associated with the client.

   The command MUST either be a valid IRC command or a three (3) digit
   number represented in ASCII text.

   IRC messages are always lines of characters terminated with a CR-LF
   (Carriage Return - Line Feed) pair, and these messages SHALL NOT
   exceed 512 characters in length, counting all characters including
   the trailing CR-LF. Thus, there are 510 characters maximum allowed
   for the command and its parameters.  There is no provision for
   continuation of message lines.  See section 6 for more details about
   current implementations.

2.3.1 Message format in Augmented BNF

   The protocol messages must be extracted from the contiguous stream of
   octets.  The current solution is to designate two characters, CR and
   LF, as message separators.  Empty messages are silently ignored,
   which permits use of the sequence CR-LF between messages without
   extra problems.

   The extracted message is parsed into the components <prefix>,
   <command> and list of parameters (<params>).

    The Augmented BNF representation for this is:

    message    =  [ ":" prefix SPACE ] command [ params ] crlf
    prefix     =  servername / ( nickname [ [ "!" user ] "@" host ] )
    command    =  1*letter / 3digit
    params     =  *14( SPACE middle ) [ SPACE ":" trailing ]
               =/ 14( SPACE middle ) [ SPACE [ ":" ] trailing ]

    nospcrlfcl =  %x01-09 / %x0B-0C / %x0E-1F / %x21-39 / %x3B-FF
                    ; any octet except NUL, CR, LF, " " and ":"
    middle     =  nospcrlfcl *( ":" / nospcrlfcl )
    trailing   =  *( ":" / " " / nospcrlfcl )

    SPACE      =  %x20        ; space character
    crlf       =  %x0D %x0A   ; "carriage return" "linefeed"

   NOTES:
      1) After extracting the parameter list, all parameters are equal
         whether matched by <middle> or <trailing>. <trailing> is just a
         syntactic trick to allow SPACE within the parameter.

      2) The NUL (%x00) character is not special in message framing, and
         basically could end up inside a parameter, but it would cause
         extra complexities in normal C string handling. Therefore, NUL
         is not allowed within messages.

   Most protocol messages specify additional semantics and syntax for
   the extracted parameter strings dictated by their position in the
   list.  For example, many server commands will assume that the first
   parameter after the command is the list of targets, which can be
   described with:

  target     =  nickname / servername Expand Multiple
  msgtarget  =  msgto *( "," msgto )
  msgto      =  channel / ( user [ "%" host ] "@" servername )
  msgto      =/ ( user "%" host ) / targetmask
  msgto      =/ nickname / ( nickname "!" user "@" host )
  channel    =  ( "#" / "+" / ( "!" channelid ) / "&" ) chanstring
                [ ":" chanstring ]
  servername =  hostname
  host       =  hostname / hostaddr
  hostname   =  shortname *( "." shortname )
  shortname  =  ( letter / digit ) *( letter / digit / "-" )
                *( letter / digit )
                  ; as specified in RFC 1123 [HNAME]
  hostaddr   =  ip4addr / ip6addr
  ip4addr    =  1*3digit "." 1*3digit "." 1*3digit "." 1*3digit
  ip6addr    =  1*hexdigit 7( ":" 1*hexdigit )
  ip6addr    =/ "0:0:0:0:0:" ( "0" / "FFFF" ) ":" ip4addr
  nickname   =  ( letter / special ) *8( letter / digit / special / "-" )
  targetmask =  ( "$" / "#" ) mask
                  ; see details on allowed masks in section 3.3.1
  chanstring = *49(%x01-06 / %x08-09 / %x0B-0C / %x0E-1F / %x21-2B / Expand Multiple
             %x2D-39 / %x3B-FF)

                  ; any octet except NUL, BELL, CR, LF, " ", "," and ":"
  channelid  = 5( %x41-5A / digit )   ; 5( A-Z / 0-9 )

  Other parameter syntaxes are:

  user       =  1*( %x01-09 / %x0B-0C / %x0E-1F / %x21-3F / %x41-FF )
                  ; any octet except NUL, CR, LF, " " and "@"
    key        =  1*23( %x01-05 / %x07-08 / %x0C / %x0E-1F / %x21-7F ) Expand
                  ; any 7-bit US_ASCII character,
                  ; except NUL, CR, LF, ACK, h/v TABs, and " "

OR

  key        =  1*23( %x01-08 / %x0E-1F / %x21-7F )
                  ; any 7-bit US_ASCII character,
                  ; except NUL, CR, LF, FF, h/v TABs, and " "
  letter     =  %x41-5A / %x61-7A       ; A-Z / a-z
  digit      =  %x30-39                 ; 0-9
  hexdigit   =  digit / "A" / "B" / "C" / "D" / "E" / "F"
  special    =  %x5B-60 / %x7B-7D
                   ; "[", "]", "\", "`", "_", "^", "{", "|", "}"

  NOTES:
      1) The <hostaddr> syntax is given here for the sole purpose of
         indicating the format to follow for IP addresses.  This
         reflects the fact that the only available implementations of
         this protocol uses TCP/IP as underlying network protocol but is
         not meant to prevent other protocols to be used.

      2) <hostname> has a maximum length of 63 characters.  This is a
         limitation of the protocol as internet hostnames (in
         particular) can be longer.  Such restriction is necessary
         because IRC messages are limited to 512 characters in length.
         Clients connecting from a host which name is longer than 63
         characters are registered using the host (numeric) address
         instead of the host name.

      3) Some parameters used in the following sections of this
         documents are not defined here as there is nothing specific
         about them besides the name that is used for convenience.
         These parameters follow the general syntax defined for
         <params>.

2.4 Numeric replies

   Most of the messages sent to the server generate a reply of some
   sort.  The most common reply is the numeric reply, used for both
   errors and normal replies.  The numeric reply MUST be sent as one
   message consisting of the sender prefix, the three-digit numeric, and
   the target of the reply.  A numeric reply is not allowed to originate
   from a client. In all other respects, a numeric reply is just like a
   normal message, except that the keyword is made up of 3 numeric
   digits rather than a string of letters.  A list of different replies
   is supplied in section 5 (Replies).

2.5 Wildcard expressions

   When wildcards are allowed in a string, it is referred as a "mask".

   For string matching purposes, the protocol allows the use of two
   special characters: '?' (%x3F) to match one and only one character,
   and '*' (%x2A) to match any number of any characters.  These two
   characters can be escaped using the character '\' (%x5C).

   The Augmented BNF syntax for this is:

    mask       =  *( nowild / noesc wildone / noesc wildmany )
    wildone    =  %x3F
    wildmany   =  %x2A
    nowild     =  %x01-29 / %x2B-3E / %x40-FF
                    ; any octet except NUL, "*", "?"
    noesc      =  %x01-5B / %x5D-FF
                    ; any octet except NUL and "\"
    matchone   =  %x01-FF
                    ; matches wildone
    matchmany  =  *matchone
                    ; matches wildmany

   Examples:

   a?c         ; Matches any string of 3 characters in length starting
               with "a" and ending with "c"

   a*c         ; Matches any string of at least 2 characters in length
               starting with "a" and ending with "c"

3. Message Details

   On the following pages there are descriptions of each message
   recognized by the IRC server and client.  All commands described in
   this section MUST be implemented by any server for this protocol.

   Where the reply ERR_NOSUCHSERVER is returned, it means that the
   target of the message could not be found.  The server MUST NOT send
   any other replies after this error for that command.

   The server to which a client is connected is required to parse the
   complete message, and return any appropriate errors.

   If multiple parameters is presented, then each MUST be checked for
   validity and appropriate responses MUST be sent back to the client.
   In the case of incorrect messages which use parameter lists with
   comma as an item separator, a reply MUST be sent for each item.

3.1 Connection Registration

   The commands described here are used to register a connection with an
   IRC server as a user as well as to correctly disconnect.

   A "PASS" command is not required for a client connection to be
   registered, but it MUST precede the latter of the NICK/USER
   combination (for a user connection) or the SERVICE command (for a
   service connection). The RECOMMENDED order for a client to register
   is as follows:

                           1. Pass message
           2. Nick message                 2. Service message
           3. User message

   Upon success, the client will receive an RPL_WELCOME (for users) or
   RPL_YOURESERVICE (for services) message indicating that the
   connection is now registered and known the to the entire IRC network.
   The reply message MUST contain the full client identifier upon which
   it was registered.

3.1.1 Password message

      Command: PASS
   Parameters: <password>

   The PASS command is used to set a 'connection password'.  The
   optional password can and MUST be set before any attempt to register
   the connection is made.  Currently this requires that user send a
   PASS command before sending the NICK/USER combination.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_ALREADYREGISTRED

   Example:

           PASS secretpasswordhere

3.1.2 Nick message


      Command: NICK
   Parameters: <nickname>

   NICK command is used to give user a nickname or change the existing
   one.

   Numeric Replies:

           ERR_NONICKNAMEGIVEN             ERR_ERRONEUSNICKNAME
           ERR_NICKNAMEINUSE               ERR_NICKCOLLISION
           ERR_UNAVAILRESOURCE             ERR_RESTRICTED

   Examples:

   NICK Wiz                ; Introducing new nick "Wiz" if session is
                           still unregistered, or user changing his
                           nickname to "Wiz"

   :WiZ!<EMAIL> NICK Kilroy
                           ; Server telling that WiZ changed his
                           nickname to Kilroy.

3.1.3 User message

      Command: USER
   Parameters: <user> <mode> <unused> <realname>

   The USER command is used at the beginning of connection to specify
   the username, hostname and realname of a new user.

   The <mode> parameter should be a numeric, and can be used to
   automatically set user modes when registering with the server.  This
   parameter is a bitmask, with only 2 bits having any signification: if
   the bit 2 is set, the user mode 'w' will be set and if the bit 3 is
   set, the user mode 'i' will be set.  (See Section 3.1.5 "User
   Modes").

   The <realname> may contain space characters.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_ALREADYREGISTRED

   Example:

   USER guest 0 * :Ronnie Reagan   ; User registering themselves with a
                                   username of "guest" and real name
                                   "Ronnie Reagan".

   USER guest 8 * :Ronnie Reagan   ; User registering themselves with a
                                   username of "guest" and real name
                                   "Ronnie Reagan", and asking to be set
                                   invisible.

3.1.4 Oper message

      Command: OPER
   Parameters: <name> <password>

   A normal user uses the OPER command to obtain operator privileges.
   The combination of <name> and <password> are REQUIRED to gain
   Operator privileges.  Upon success, the user will receive a MODE
   message (see section 3.1.5) indicating the new user modes.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              RPL_YOUREOPER
           ERR_NOOPERHOST                  ERR_PASSWDMISMATCH

   Example:

   OPER foo bar                    ; Attempt to register as an operator
                                   using a username of "foo" and "bar"
                                   as the password.

3.1.5 User mode message

      Command: MODE
   Parameters: <nickname>
               *( ( "+" / "-" ) *( "i" / "w" / "o" / "O" / "r" ) )

   The user MODE's are typically changes which affect either how the
   client is seen by others or what 'extra' messages the client is sent.

   A user MODE command MUST only be accepted if both the sender of the
   message and the nickname given as a parameter are both the same.  If
   no other parameter is given, then the server will return the current
   settings for the nick.

      The available modes are as follows:

           a - user is flagged as away;
           i - marks a users as invisible;
           w - user receives wallops;
           r - restricted user connection;
           o - operator flag;
           O - local operator flag;
           s - marks a user for receipt of server notices.

   Additional modes may be available later on.

   The flag 'a' SHALL NOT be toggled by the user using the MODE command,
   instead use of the AWAY command is REQUIRED.

   If a user attempts to make themselves an operator using the "+o" or
   "+O" flag, the attempt SHOULD be ignored as users could bypass the
   authentication mechanisms of the OPER command.  There is no
   restriction, however, on anyone `deopping' themselves (using "-o" or
   "-O").

   On the other hand, if a user attempts to make themselves unrestricted
   using the "-r" flag, the attempt SHOULD be ignored.  There is no
   restriction, however, on anyone `deopping' themselves (using "+r").
   This flag is typically set by the server upon connection for
   administrative reasons.  While the restrictions imposed are left up
   to the implementation, it is typical that a restricted user not be
   allowed to change nicknames, nor make use of the channel operator
   status on channels.

   The flag 's' is obsolete but MAY still be used.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_USERSDONTMATCH
           ERR_UMODEUNKNOWNFLAG            RPL_UMODEIS

   Examples:

   MODE WiZ -w                     ; Command by WiZ to turn off
                                   reception of WALLOPS messages.

   MODE Angel +i                   ; Command from Angel to make herself
                                   invisible.

   MODE WiZ -o                     ; WiZ 'deopping' (removing operator
                                   status).

3.1.6 Service message

      Command: SERVICE
   Parameters: <nickname> <reserved> <distribution> <type>
               <reserved> <info>

   The SERVICE command to register a new service.  Command parameters
   specify the service nickname, distribution, type and info of a new
   service.

   The <distribution> parameter is used to specify the visibility of a
   service.  The service may only be known to servers which have a name
   matching the distribution.  For a matching server to have knowledge
   of the service, the network path between that server and the server
   on which the service is connected MUST be composed of servers which
   names all match the mask.

   The <type> parameter is currently reserved for future usage.

   Numeric Replies:

           ERR_ALREADYREGISTRED            ERR_NEEDMOREPARAMS
           ERR_ERRONEUSNICKNAME
           RPL_YOURESERVICE                RPL_YOURHOST
           RPL_MYINFO

   Example:

   SERVICE dict * *.fr 0 0 :French Dictionary ; Service registering
                                   itself with a name of "dict".  This
                                   service will only be available on
                                   servers which name matches "*.fr".

3.1.7 Quit

      Command: QUIT
   Parameters: [ <Quit Message> ]

   A client session is terminated with a quit message.  The server
   acknowledges this by sending an ERROR message to the client.

   Numeric Replies:

           None.

   Example:

   QUIT :Gone to have lunch        ; Preferred message format.

   :syrk!<EMAIL> QUIT :Gone to have lunch ; User
                                   syrk has quit IRC to have lunch.

3.1.8 Squit

      Command: SQUIT
   Parameters: <server> <comment>

   The SQUIT command is available only to operators.  It is used to
   disconnect server links.  Also servers can generate SQUIT messages on
   error conditions.  A SQUIT message may also target a remote server
   connection.  In this case, the SQUIT message will simply be sent to
   the remote server without affecting the servers in between the
   operator and the remote server.

   The <comment> SHOULD be supplied by all operators who execute a SQUIT
   for a remote server.  The server ordered to disconnect its peer
   generates a WALLOPS message with <comment> included, so that other
   users may be aware of the reason of this action.

   Numeric replies:

           ERR_NOPRIVILEGES                ERR_NOSUCHSERVER
           ERR_NEEDMOREPARAMS

   Examples:

   SQUIT tolsun.oulu.fi :Bad Link ?  ; Command to uplink of the server
                                   tolson.oulu.fi to terminate its
                                   connection with comment "Bad Link".

   :Trillian SQUIT cm22.eng.umd.edu :Server out of control ; Command
                                   from Trillian from to disconnect
                                   "cm22.eng.umd.edu" from the net with
                                   comment "Server out of control".

3.2 Channel operations

   This group of messages is concerned with manipulating channels, their
   properties (channel modes), and their contents (typically users).
   For this reason, these messages SHALL NOT be made available to
   services.

   All of these messages are requests which will or will not be granted
   by the server.  The server MUST send a reply informing the user
   whether the request was granted, denied or generated an error.  When
   the server grants the request, the message is typically sent back
   (eventually reformatted) to the user with the prefix set to the user
   itself.

   The rules governing how channels are managed are enforced by the
   servers.  These rules are beyond the scope of this document.  More
   details are found in "Internet Relay Chat: Channel Management" [IRC-
   CHAN].

3.2.1 Join message

      Command: JOIN
   Parameters: ( <channel> *( "," <channel> ) [ <key> *( "," <key> ) ] )
               / "0"

   The JOIN command is used by a user to request to start listening to
   the specific channel.  Servers MUST be able to parse arguments in the
   form of a list of target, but SHOULD NOT use lists when sending JOIN
   messages to clients.

   Once a user has joined a channel, he receives information about
   all commands his server receives affecting the channel.  This
   includes JOIN, MODE, KICK, PART, QUIT and of course PRIVMSG/NOTICE.
   This allows channel members to keep track of the other channel
   members, as well as channel modes.

   If a JOIN is successful, the user receives a JOIN message as
   confirmation and is then sent the channel's topic (using RPL_TOPIC) and
   the list of users who are on the channel (using RPL_NAMREPLY), which
   MUST include the user joining.

   Note that this message accepts a special argument ("0"), which is
   a special request to leave all channels the user is currently a member
   of.  The server will process this message as if the user had sent
   a PART command (See Section 3.2.2) for each channel he is a member
   of.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_BANNEDFROMCHAN
           ERR_INVITEONLYCHAN              ERR_BADCHANNELKEY
           ERR_CHANNELISFULL               ERR_BADCHANMASK
           ERR_NOSUCHCHANNEL               ERR_TOOMANYCHANNELS
           ERR_TOOMANYTARGETS              ERR_UNAVAILRESOURCE
           RPL_TOPIC

   Examples:

   JOIN #foobar                    ; Command to join channel #foobar.

   JOIN &foo fubar                 ; Command to join channel &foo using
                                   key "fubar".

   JOIN #foo,&bar fubar            ; Command to join channel #foo using
                                   key "fubar" and &bar using no key.

   JOIN #foo,#bar fubar,foobar     ; Command to join channel #foo using
                                   key "fubar", and channel #bar using
                                   key "foobar".

   JOIN #foo,#bar                  ; Command to join channels #foo and
                                   #bar.

   JOIN 0                          ; Leave all currently joined
                                   channels.

   :WiZ!<EMAIL> JOIN #Twilight_zone ; JOIN message from WiZ
                                   on channel #Twilight_zone

3.2.2 Part message

      Command: PART
   Parameters: <channel> *( "," <channel> ) [ <Part Message> ]

   The PART command causes the user sending the message to be removed
   from the list of active members for all given channels listed in the
   parameter string.  If a "Part Message" is given, this will be sent
   instead of the default message, the nickname.  This request is always
   granted by the server.

   Servers MUST be able to parse arguments in the form of a list of
   target, but SHOULD NOT use lists when sending PART messages to
   clients.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_NOSUCHCHANNEL
           ERR_NOTONCHANNEL

   Examples:

   PART #twilight_zone             ; Command to leave channel
                                   "#twilight_zone"

   PART #oz-ops,&group5            ; Command to leave both channels
                                   "&group5" and "#oz-ops".

   :WiZ!<EMAIL> PART #playzone :I lost
                                   ; User WiZ leaving channel
                                   "#playzone" with the message "I
                                   lost".

3.2.3 Channel mode message


EID 2991 (Verified) is as follows:

Section: 3.2.3

Original Text:

The original text is missing.

Corrected Text:

ERR_NOSUCHCHANNEL

Notes:

Numeric reply list for Channel Modes should include ERR_NOSUCHCHANNEL.

      Command: MODE
   Parameters: <channel> *( ( "-" / "+" ) *<modes> *<modeparams> )

   The MODE command is provided so that users may query and change the
   characteristics of a channel.  For more details on available modes
   and their uses, see "Internet Relay Chat: Channel Management" [IRC-
   CHAN].  Note that there is a maximum limit of three (3) changes per
   command for modes that take a parameter.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_KEYSET
           ERR_NOCHANMODES                 ERR_CHANOPRIVSNEEDED
           ERR_USERNOTINCHANNEL            ERR_UNKNOWNMODE
           RPL_CHANNELMODEIS
           RPL_BANLIST                     RPL_ENDOFBANLIST
           RPL_EXCEPTLIST                  RPL_ENDOFEXCEPTLIST
           RPL_INVITELIST                  RPL_ENDOFINVITELIST
           RPL_UNIQOPIS

   The following examples are given to help understanding the syntax of
   the MODE command, but refer to modes defined in "Internet Relay Chat:
   Channel Management" [IRC-CHAN].

   Examples:

   MODE #Finnish +imI *!*@*.fi     ; Command to make #Finnish channel
                                   moderated and 'invite-only' with user
                                   with a hostname matching *.fi
                                   automatically invited.

   MODE #Finnish +o Kilroy         ; Command to give 'chanop' privileges
                                   to Kilroy on channel #Finnish.

   MODE #Finnish +v Wiz            ; Command to allow WiZ to speak on
                                   #Finnish.

   MODE #Fins -s                   ; Command to remove 'secret' flag
                                   from channel #Fins.

   MODE #42 +k oulu                ; Command to set the channel key to
                                   "oulu".

   MODE #42 -k oulu                ; Command to remove the "oulu"
                                   channel key on channel "#42".

   MODE #eu-opers +l 10            ; Command to set the limit for the
                                   number of users on channel
                                   "#eu-opers" to 10.

   :WiZ!<EMAIL> MODE #eu-opers -l
                                   ; User "WiZ" removing the limit for
                                   the number of users on channel "#eu-
                                   opers".

   MODE &oulu +b                   ; Command to list ban masks set for
                                   the channel "&oulu".

   MODE &oulu +b *!*@*             ; Command to prevent all users from
                                   joining.

   MODE &oulu +b *!*@*.edu +e *!*@*.bu.edu
                                   ; Command to prevent any user from a
                                   hostname matching *.edu from joining,
                                   except if matching *.bu.edu

   MODE #bu +be *!*@*.edu *!*@*.bu.edu
                                   ; Comment to prevent any user from a
                                   hostname matching *.edu from joining,
                                   except if matching *.bu.edu

   MODE #meditation e              ; Command to list exception masks set
                                   for the channel "#meditation".

   MODE #meditation I              ; Command to list invitations masks
                                   set for the channel "#meditation".

   MODE !12345ircd O               ; Command to ask who the channel
                                   creator for "!12345ircd" is

3.2.4 Topic message


EID 3001 (Verified) is as follows:

Section: 3.2.4

Original Text:

The original text is missing.

Corrected Text:

ERR_NOSUCHCHANNEL

Notes:

Numeric reply list for Topic should include ERR_NOSUCHCHANNEL.

      Command: TOPIC
   Parameters: <channel> [ <topic> ]

   The TOPIC command is used to change or view the topic of a channel.
   The topic for channel <channel> is returned if there is no <topic>
   given.  If the <topic> parameter is present, the topic for that
   channel will be changed, if this action is allowed for the user
   requesting it.  If the <topic> parameter is an empty string, the
   topic for that channel will be removed.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_NOTONCHANNEL
           RPL_NOTOPIC                     RPL_TOPIC
           ERR_CHANOPRIVSNEEDED            ERR_NOCHANMODES

   Examples:

   :WiZ!<EMAIL> TOPIC #test :New topic ; User Wiz setting the
                                   topic.

   TOPIC #test :another topic      ; Command to set the topic on #test
                                   to "another topic".

   TOPIC #test :                   ; Command to clear the topic on
                                   #test.

   TOPIC #test                     ; Command to check the topic for
                                   #test.

3.2.5 Names message

      Command: NAMES
   Parameters: [ <channel> *( "," <channel> ) [ <target> ] ]

   By using the NAMES command, a user can list all nicknames that are
   visible to him. For more details on what is visible and what is not,
   see "Internet Relay Chat: Channel Management" [IRC-CHAN].  The
   <channel> parameter specifies which channel(s) to return information
   about.  There is no error reply for bad channel names.

   If no <channel> parameter is given, a list of all channels and their
   occupants is returned.  At the end of this list, a list of users who
   are visible but either not on any channel or not on a visible channel
   are listed as being on `channel' "*".

   If the <target> parameter is specified, the request is forwarded to
   that server which will generate the reply.

   Wildcards are allowed in the <target> parameter.

   Numerics:

           ERR_TOOMANYMATCHES              ERR_NOSUCHSERVER
           RPL_NAMREPLY                    RPL_ENDOFNAMES

   Examples:

   NAMES #twilight_zone,#42        ; Command to list visible users on
                                   #twilight_zone and #42

   NAMES                           ; Command to list all visible
                                   channels and users

3.2.6 List message

      Command: LIST
   Parameters: [ <channel> *( "," <channel> ) [ <target> ] ]

   The list command is used to list channels and their topics.  If the
   <channel> parameter is used, only the status of that channel is
   displayed.

   If the <target> parameter is specified, the request is forwarded to
   that server which will generate the reply.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_TOOMANYMATCHES              ERR_NOSUCHSERVER
           RPL_LIST                        RPL_LISTEND

   Examples:

   LIST                            ; Command to list all channels.

   LIST #twilight_zone,#42         ; Command to list channels
                                   #twilight_zone and #42

3.2.7 Invite message

      Command: INVITE
   Parameters: <nickname> <channel>

   The INVITE command is used to invite a user to a channel.  The
   parameter <nickname> is the nickname of the person to be invited to
   the target channel <channel>.  There is no requirement that the
   channel the target user is being invited to must exist or be a valid
   channel.  However, if the channel exists, only members of the channel
   are allowed to invite other users.  When the channel has invite-only
   flag set, only channel operators may issue INVITE command.

   Only the user inviting and the user being invited will receive
   notification of the invitation.  Other channel members are not
   notified.  (This is unlike the MODE changes, and is occasionally the
   source of trouble for users.)

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_NOSUCHNICK
           ERR_NOTONCHANNEL                ERR_USERONCHANNEL
           ERR_CHANOPRIVSNEEDED
           RPL_INVITING                    RPL_AWAY

   Examples:

   :Angel!<EMAIL> INVITE Wiz #Dust

                                   ; Message to WiZ when he has been
                                   invited by user Angel to channel
                                   #Dust

   INVITE Wiz #Twilight_Zone       ; Command to invite WiZ to
                                   #Twilight_zone

3.2.8 Kick command

      Command: KICK
   Parameters: <channel> *( "," <channel> ) <user> *( "," <user> )
               [<comment>]

   The KICK command can be used to request the forced removal of a user
   from a channel.  It causes the <user> to PART from the <channel> by
   force.  For the message to be syntactically correct, there MUST be
   either one channel parameter and multiple user parameter, or as many
   channel parameters as there are user parameters.  If a "comment" is
   given, this will be sent instead of the default message, the nickname
   of the user issuing the KICK.

   The server MUST NOT send KICK messages with multiple channels or
   users to clients.  This is necessarily to maintain backward
   compatibility with old client software.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_NOSUCHCHANNEL
           ERR_BADCHANMASK                 ERR_CHANOPRIVSNEEDED
           ERR_USERNOTINCHANNEL            ERR_NOTONCHANNEL

   Examples:

   KICK &Melbourne Matthew         ; Command to kick Matthew from
                                   &Melbourne

   KICK #Finnish John :Speaking English
                                   ; Command to kick John from #Finnish
                                   using "Speaking English" as the
                                   reason (comment).

   :WiZ!<EMAIL> KICK #Finnish John
                                   ; KICK message on channel #Finnish
                                   from WiZ to remove John from channel

3.3 Sending messages

   The main purpose of the IRC protocol is to provide a base for clients
   to communicate with each other.  PRIVMSG, NOTICE and SQUERY
   (described in Section 3.5 on Service Query and Commands) are the only
   messages available which actually perform delivery of a text message
   from one client to another - the rest just make it possible and try
   to ensure it happens in a reliable and structured manner.

3.3.1 Private messages

      Command: PRIVMSG
   Parameters: <msgtarget> <text to be sent>

   PRIVMSG is used to send private messages between users, as well as to
   send messages to channels.  <msgtarget> is usually the nickname of
   the recipient of the message, or a channel name.

   The <msgtarget> parameter may also be a host mask (#<mask>) or server
   mask ($<mask>).  In both cases the server will only send the PRIVMSG
   to those who have a server or host matching the mask.  The mask MUST
   have at least 1 (one) "." in it and no wildcards following the last
   ".".  This requirement exists to prevent people sending messages to
   "#*" or "$*", which would broadcast to all users.  Wildcards are the
   '*' and '?'  characters.  This extension to the PRIVMSG command is
   only available to operators.

   Numeric Replies:

           ERR_NORECIPIENT                 ERR_NOTEXTTOSEND
           ERR_CANNOTSENDTOCHAN            ERR_NOTOPLEVEL
           ERR_WILDTOPLEVEL                ERR_TOOMANYTARGETS
           ERR_NOSUCHNICK
           RPL_AWAY

   Examples:

   :Angel!<EMAIL> PRIVMSG Wiz :Are you receiving this message ?
                                   ; Message from Angel to Wiz.

   PRIVMSG Angel :yes I'm receiving it !
                                   ; Command to send a message to Angel.

   PRIVMSG <EMAIL> :Hello !
                                   ; Command to send a message to a user
                                   on server tolsun.oulu.fi with
                                   username of "jto".

   PRIVMSG <EMAIL> :Are you a frog?
                                   ; Message to a user on server
                                   irc.stealth.net with username of
                                   "kalt", and connected from the host
                                   millennium.stealth.net.

   PRIVMSG kalt%millennium.stealth.net :Do you like cheese?
                                   ; Message to a user on the local
                                   server with username of "kalt", and
                                   connected from the host
                                   millennium.stealth.net.

   PRIVMSG Wiz!<EMAIL> :Hello !
                                   ; Message to the user with nickname
                                   Wiz who is connected from the host
                                   tolsun.oulu.fi and has the username
                                   "jto".

   PRIVMSG $*.fi :Server tolsun.oulu.fi rebooting.
                                   ; Message to everyone on a server
                                   which has a name matching *.fi.

   PRIVMSG #*.edu :NSFNet is undergoing work, expect interruptions
                                   ; Message to all users who come from
                                   a host which has a name matching
                                   *.edu.

3.3.2 Notice

      Command: NOTICE
   Parameters: <msgtarget> <text>

   The NOTICE command is used similarly to PRIVMSG.  The difference
   between NOTICE and PRIVMSG is that automatic replies MUST NEVER be
   sent in response to a NOTICE message.  This rule applies to servers

   too - they MUST NOT send any error reply back to the client on
   receipt of a notice.  The object of this rule is to avoid loops
   between clients automatically sending something in response to
   something it received.

   This command is available to services as well as users.

   This is typically used by services, and automatons (clients with
   either an AI or other interactive program controlling their actions).

   See PRIVMSG for more details on replies and examples.

3.4 Server queries and commands

   The server query group of commands has been designed to return
   information about any server which is connected to the network.

   In these queries, where a parameter appears as <target>, wildcard
   masks are usually valid.  For each parameter, however, only one query
   and set of replies is to be generated.  In most cases, if a nickname
   is given, it will mean the server to which the user is connected.

   These messages typically have little value for services, it is
   therefore RECOMMENDED to forbid services from using them.

3.4.1 Motd message

      Command: MOTD
   Parameters: [ <target> ]

   The MOTD command is used to get the "Message Of The Day" of the given
   server, or current server if <target> is omitted.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:
           RPL_MOTDSTART                   RPL_MOTD
           RPL_ENDOFMOTD                   ERR_NOMOTD

3.4.2 Lusers message

      Command: LUSERS
   Parameters: [ <mask> [ <target> ] ]

   The LUSERS command is used to get statistics about the size of the
   IRC network.  If no parameter is given, the reply will be about the
   whole net.  If a <mask> is specified, then the reply will only

   concern the part of the network formed by the servers matching the
   mask.  Finally, if the <target> parameter is specified, the request
   is forwarded to that server which will generate the reply.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           RPL_LUSERCLIENT                 RPL_LUSEROP
           RPL_LUSERUNKOWN                 RPL_LUSERCHANNELS
           RPL_LUSERME                     ERR_NOSUCHSERVER

3.4.3 Version message

      Command: VERSION
   Parameters: [ <target> ]

   The VERSION command is used to query the version of the server
   program.  An optional parameter <target> is used to query the version
   of the server program which a client is not directly connected to.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER                RPL_VERSION

   Examples:

   VERSION tolsun.oulu.fi          ; Command to check the version of
                                   server "tolsun.oulu.fi".

3.4.4 Stats message

      Command: STATS
   Parameters: [ <query> [ <target> ] ]

   The stats command is used to query statistics of certain server.  If
   <query> parameter is omitted, only the end of stats reply is sent
   back.

   A query may be given for any single letter which is only checked by
   the destination server and is otherwise passed on by intermediate
   servers, ignored and unaltered.

   Wildcards are allowed in the <target> parameter.

   Except for the ones below, the list of valid queries is
   implementation dependent.  The standard queries below SHOULD be
   supported by the server:

            l - returns a list of the server's connections, showing how
                long each connection has been established and the
                traffic over that connection in Kbytes and messages for
                each direction;
            m - returns the usage count for each of commands supported
                by the server; commands for which the usage count is
                zero MAY be omitted;
            o - returns a list of configured privileged users,
                operators;
            u - returns a string showing how long the server has been
                up.

   It is also RECOMMENDED that client and server access configuration be
   published this way.

   Numeric Replies:

           ERR_NOSUCHSERVER
           RPL_STATSLINKINFO                RPL_STATSUPTIME
           RPL_STATSCOMMANDS                RPL_STATSOLINE
           RPL_ENDOFSTATS

   Examples:

   STATS m                         ; Command to check the command usage
                                   for the server you are connected to

3.4.5 Links message

      Command: LINKS
   Parameters: [ [ <remote server> ] <server mask> ]

   With LINKS, a user can list all servernames, which are known by the
   server answering the query.  The returned list of servers MUST match
   the mask, or if no mask is given, the full list is returned.

   If <remote server> is given in addition to <server mask>, the LINKS
   command is forwarded to the first server found that matches that name
   (if any), and that server is then required to answer the query.

   Numeric Replies:

           ERR_NOSUCHSERVER
           RPL_LINKS                        RPL_ENDOFLINKS

   Examples:

   LINKS *.au                      ; Command to list all servers which
                                   have a name that matches *.au;

   LINKS *.edu *.bu.edu            ; Command to list servers matching
                                   *.bu.edu as seen by the first server
                                   matching *.edu.

3.4.6 Time message

      Command: TIME
   Parameters: [ <target> ]

   The time command is used to query local time from the specified
   server. If the <target> parameter is not given, the server receiving
   the command must reply to the query.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER              RPL_TIME

   Examples:
   TIME tolsun.oulu.fi             ; check the time on the server
                                   "tolson.oulu.fi"

3.4.7 Connect message

      Command: CONNECT
   Parameters: <target server> <port> [ <remote server> ]

   The CONNECT command can be used to request a server to try to
   establish a new connection to another server immediately.  CONNECT is
   a privileged command and SHOULD be available only to IRC Operators.
   If a <remote server> is given and its mask doesn't match name of the
   parsing server, the CONNECT attempt is sent to the first match of
   remote server. Otherwise the CONNECT attempt is made by the server
   processing the request.

   The server receiving a remote CONNECT command SHOULD generate a
   WALLOPS message describing the source and target of the request.

   Numeric Replies:

           ERR_NOSUCHSERVER              ERR_NOPRIVILEGES
           ERR_NEEDMOREPARAMS

   Examples:

   CONNECT tolsun.oulu.fi 6667     ; Command to attempt to connect local
                                   server to tolsun.oulu.fi on port 6667

3.4.8 Trace message

      Command: TRACE
   Parameters: [ <target> ]

   TRACE command is used to find the route to specific server and
   information about its peers.  Each server that processes this command
   MUST report to the sender about it.  The replies from pass-through
   links form a chain, which shows route to destination.  After sending
   this reply back, the query MUST be sent to the next server until
   given <target> server is reached.

   TRACE command is used to find the route to specific server.  Each
   server that processes this message MUST tell the sender about it by
   sending a reply indicating it is a pass-through link, forming a chain
   of replies.  After sending this reply back, it MUST then send the
   TRACE message to the next server until given server is reached.  If
   the <target> parameter is omitted, it is RECOMMENDED that TRACE
   command sends a message to the sender telling which servers the local
   server has direct connection to.

   If the destination given by <target> is an actual server, the
   destination server is REQUIRED to report all servers, services and
   operators which are connected to it; if the command was issued by an
   operator, the server MAY also report all users which are connected to
   it.  If the destination given by <target> is a nickname, then only a
   reply for that nickname is given.  If the <target> parameter is
   omitted, it is RECOMMENDED that the TRACE command is parsed as
   targeted to the processing server.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER

      If the TRACE message is destined for another server, all
      intermediate servers must return a RPL_TRACELINK reply to indicate
      that the TRACE passed through it and where it is going next.

           RPL_TRACELINK

      A TRACE reply may be composed of any number of the following
      numeric replies.

           RPL_TRACECONNECTING           RPL_TRACEHANDSHAKE
           RPL_TRACEUNKNOWN              RPL_TRACEOPERATOR
           RPL_TRACEUSER                 RPL_TRACESERVER
           RPL_TRACESERVICE              RPL_TRACENEWTYPE
           RPL_TRACECLASS                RPL_TRACELOG
           RPL_TRACEEND

   Examples:

   TRACE *.oulu.fi                 ; TRACE to a server matching
                                   *.oulu.fi

3.4.9 Admin command

      Command: ADMIN
   Parameters: [ <target> ]

   The admin command is used to find information about the administrator
   of the given server, or current server if <target> parameter is
   omitted.  Each server MUST have the ability to forward ADMIN messages
   to other servers.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER
           RPL_ADMINME                   RPL_ADMINLOC1
           RPL_ADMINLOC2                 RPL_ADMINEMAIL

   Examples:

   ADMIN tolsun.oulu.fi            ; request an ADMIN reply from
                                   tolsun.oulu.fi

   ADMIN syrk                      ; ADMIN request for the server to
                                   which the user syrk is connected

3.4.10 Info command

      Command: INFO
   Parameters: [ <target> ]

   The INFO command is REQUIRED to return information describing the
   server: its version, when it was compiled, the patchlevel, when it
   was started, and any other miscellaneous information which may be
   considered to be relevant.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER
           RPL_INFO                      RPL_ENDOFINFO

   Examples:

   INFO csd.bu.edu                 ; request an INFO reply from
                                   csd.bu.edu

   INFO Angel                      ; request info from the server that
                                   Angel is connected to.

3.5 Service Query and Commands

   The service query group of commands has been designed to return
   information about any service which is connected to the network.

3.5.1 Servlist message

      Command: SERVLIST
   Parameters: [ <mask> [ <type> ] ]

   The SERVLIST command is used to list services currently connected to
   the network and visible to the user issuing the command.  The
   optional parameters may be used to restrict the result of the query
   (to matching services names, and services type).

   Numeric Replies:

           RPL_SERVLIST                  RPL_SERVLISTEND

3.5.2 Squery

      Command: SQUERY
   Parameters: <servicename> <text>

   The SQUERY command is used similarly to PRIVMSG.  The only difference
   is that the recipient MUST be a service.  This is the only way for a
   text message to be delivered to a service.

   See PRIVMSG for more details on replies and example.

   Examples:

   SQUERY irchelp :HELP privmsg
                                   ; Message to the service with
                                   nickname irchelp.

   SQUERY <EMAIL> :fr2en blaireau
                                   ; Message to the service with name
                                   <EMAIL>.

3.6 User based queries

   User queries are a group of commands which are primarily concerned
   with finding details on a particular user or group users.  When using
   wildcards with any of these commands, if they match, they will only
   return information on users who are 'visible' to you.  The visibility
   of a user is determined as a combination of the user's mode and the
   common set of channels you are both on.

   Although services SHOULD NOT be using this class of message, they are
   allowed to.

3.6.1 Who query

      Command: WHO
   Parameters: [ <mask> [ "o" ] ]

   The WHO command is used by a client to generate a query which returns
   a list of information which 'matches' the <mask> parameter given by
   the client.  In the absence of the <mask> parameter, all visible
   (users who aren't invisible (user mode +i) and who don't have a
   common channel with the requesting client) are listed.  The same
   result can be achieved by using a <mask> of "0" or any wildcard which
   will end up matching every visible user.

   The <mask> passed to WHO is matched against users' host, server, real
   name and nickname if the channel <mask> cannot be found.

   If the "o" parameter is passed only operators are returned according
   to the <mask> supplied.

   Numeric Replies:

           ERR_NOSUCHSERVER
           RPL_WHOREPLY                  RPL_ENDOFWHO

   Examples:

   WHO *.fi                        ; Command to list all users who match
                                   against "*.fi".

   WHO jto* o                      ; Command to list all users with a
                                   match against "jto*" if they are an
                                   operator.

3.6.2 Whois query

      Command: WHOIS
   Parameters: [ <target> ] <mask> *( "," <mask> )

   This command is used to query information about particular user.
   The server will answer this command with several numeric messages
   indicating different statuses of each user which matches the mask (if
   you are entitled to see them).  If no wildcard is present in the
   <mask>, any information about that nick which you are allowed to see
   is presented.

   If the <target> parameter is specified, it sends the query to a
   specific server.  It is useful if you want to know how long the user
   in question has been idle as only local server (i.e., the server the
   user is directly connected to) knows that information, while
   everything else is globally known.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NOSUCHSERVER              ERR_NONICKNAMEGIVEN
           RPL_WHOISUSER                 RPL_WHOISCHANNELS
           RPL_WHOISCHANNELS             RPL_WHOISSERVER
           RPL_AWAY                      RPL_WHOISOPERATOR
           RPL_WHOISIDLE                 ERR_NOSUCHNICK
           RPL_ENDOFWHOIS

   Examples:

   WHOIS wiz                       ; return available user information
                                   about nick WiZ

   WHOIS eff.org trillian          ; ask server eff.org for user
                                   information  about trillian

3.6.3 Whowas

      Command: WHOWAS
   Parameters: <nickname> *( "," <nickname> ) [ <count> [ <target> ] ]

   Whowas asks for information about a nickname which no longer exists.
   This may either be due to a nickname change or the user leaving IRC.
   In response to this query, the server searches through its nickname
   history, looking for any nicks which are lexically the same (no wild
   card matching here).  The history is searched backward, returning the
   most recent entry first.  If there are multiple entries, up to
   <count> replies will be returned (or all of them if no <count>
   parameter is given).  If a non-positive number is passed as being
   <count>, then a full search is done.

   Wildcards are allowed in the <target> parameter.

   Numeric Replies:

           ERR_NONICKNAMEGIVEN           ERR_WASNOSUCHNICK
           RPL_WHOWASUSER                RPL_WHOISSERVER
           RPL_ENDOFWHOWAS

   Examples:

   WHOWAS Wiz                      ; return all information in the nick
                                   history about nick "WiZ";

   WHOWAS Mermaid 9                ; return at most, the 9 most recent
                                   entries in the nick history for
                                   "Mermaid";

   WHOWAS Trillian 1 *.edu         ; return the most recent history for
                                   "Trillian" from the first server
                                   found to match "*.edu".

3.7 Miscellaneous messages

   Messages in this category do not fit into any of the above categories
   but are nonetheless still a part of and REQUIRED by the protocol.

3.7.1 Kill message

      Command: KILL
   Parameters: <nickname> <comment>

   The KILL command is used to cause a client-server connection to be
   closed by the server which has the actual connection.  Servers
   generate KILL messages on nickname collisions.  It MAY also be
   available available to users who have the operator status.

   Clients which have automatic reconnect algorithms effectively make
   this command useless since the disconnection is only brief.  It does
   however break the flow of data and can be used to stop large amounts
   of 'flooding' from abusive users or accidents.  Abusive users usually
   don't care as they will reconnect promptly and resume their abusive
   behaviour.  To prevent this command from being abused, any user may
   elect to receive KILL messages generated for others to keep an 'eye'
   on would be trouble spots.

   In an arena where nicknames are REQUIRED to be globally unique at all
   times, KILL messages are sent whenever 'duplicates' are detected
   (that is an attempt to register two users with the same nickname) in
   the hope that both of them will disappear and only 1 reappear.

   When a client is removed as the result of a KILL message, the server
   SHOULD add the nickname to the list of unavailable nicknames in an
   attempt to avoid clients to reuse this name immediately which is
   usually the pattern of abusive behaviour often leading to useless
   "KILL loops".  See the "IRC Server Protocol" document [IRC-SERVER]
   for more information on this procedure.

   The comment given MUST reflect the actual reason for the KILL.  For
   server-generated KILLs it usually is made up of details concerning
   the origins of the two conflicting nicknames.  For users it is left
   up to them to provide an adequate reason to satisfy others who see
   it.  To prevent/discourage fake KILLs from being generated to hide
   the identify of the KILLer, the comment also shows a 'kill-path'
   which is updated by each server it passes through, each prepending
   its name to the path.

   Numeric Replies:

           ERR_NOPRIVILEGES              ERR_NEEDMOREPARAMS
           ERR_NOSUCHNICK                ERR_CANTKILLSERVER

   NOTE:
   It is RECOMMENDED that only Operators be allowed to kill other users
   with KILL command.  This command has been the subject of many
   controversies over the years, and along with the above
   recommendation, it is also widely recognized that not even operators
   should be allowed to kill users on remote servers.

3.7.2 Ping message

      Command: PING
   Parameters: <server1> [ <server2> ]

   The PING command is used to test the presence of an active client or
   server at the other end of the connection.  Servers send a PING
   message at regular intervals if no other activity detected coming
   from a connection.  If a connection fails to respond to a PING
   message within a set amount of time, that connection is closed.  A
   PING message MAY be sent even if the connection is active.

   When a PING message is received, the appropriate PONG message MUST be
   sent as reply to <server1> (server which sent the PING message out)
   as soon as possible.  If the <server2> parameter is specified, it
   represents the target of the ping, and the message gets forwarded
   there.

   Numeric Replies:

           ERR_NOORIGIN                  ERR_NOSUCHSERVER

   Examples:

   PING tolsun.oulu.fi             ; Command to send a PING message to
                                   server

   PING WiZ tolsun.oulu.fi         ; Command from WiZ to send a PING
                                   message to server "tolsun.oulu.fi"

   PING :irc.funet.fi              ; Ping message sent by server
                                   "irc.funet.fi"

3.7.3 Pong message

      Command: PONG
   Parameters: <server> [ <server2> ]

   PONG message is a reply to ping message.  If parameter <server2> is
   given, this message MUST be forwarded to given target.  The <server>
   parameter is the name of the entity who has responded to PING message
   and generated this message.

   Numeric Replies:

           ERR_NOORIGIN                  ERR_NOSUCHSERVER

   Example:

   PONG csd.bu.edu tolsun.oulu.fi  ; PONG message from csd.bu.edu to
                                   tolsun.oulu.fi

3.7.4 Error

      Command: ERROR
   Parameters: <error message>

   The ERROR command is for use by servers when reporting a serious or
   fatal error to its peers.  It may also be sent from one server to
   another but MUST NOT be accepted from any normal unknown clients.

   Only an ERROR message SHOULD be used for reporting errors which occur
   with a server-to-server link.  An ERROR message is sent to the server
   at the other end (which reports it to appropriate local users and
   logs) and to appropriate local users and logs.  It is not to be
   passed onto any other servers by a server if it is received from a
   server.

   The ERROR message is also used before terminating a client
   connection.

   When a server sends a received ERROR message to its operators, the
   message SHOULD be encapsulated inside a NOTICE message, indicating
   that the client was not responsible for the error.

   Numerics:

           None.

   Examples:

   ERROR :Server *.fi already exists ; ERROR message to the other server
                                   which caused this error.

   NOTICE WiZ :ERROR from csd.bu.edu -- Server *.fi already exists
                                   ; Same ERROR message as above but
                                   sent to user WiZ on the other server.

4. Optional features

   This section describes OPTIONAL messages.  They are not required in a
   working server implementation of the protocol described herein.  In
   the absence of the feature, an error reply message MUST be generated
   or an unknown command error.  If the message is destined for another
   server to answer then it MUST be passed on (elementary parsing
   REQUIRED) The allocated numerics for this are listed with the
   messages below.

   From this section, only the USERHOST and ISON messages are available
   to services.

4.1 Away

      Command: AWAY
   Parameters: [ <text> ]

   With the AWAY command, clients can set an automatic reply string for
   any PRIVMSG commands directed at them (not to a channel they are on).
   The server sends an automatic reply to the client sending the PRIVMSG
   command.  The only replying server is the one to which the sending
   client is connected to.

   The AWAY command is used either with one parameter, to set an AWAY
   message, or with no parameters, to remove the AWAY message.

   Because of its high cost (memory and bandwidth wise), the AWAY
   message SHOULD only be used for client-server communication.  A
   server MAY choose to silently ignore AWAY messages received from
   other servers.  To update the away status of a client across servers,
   the user mode 'a' SHOULD be used instead.  (See Section 3.1.5)

   Numeric Replies:

           RPL_UNAWAY                    RPL_NOWAWAY

   Example:

   AWAY :Gone to lunch.  Back in 5 ; Command to set away message to
                                   "Gone to lunch.  Back in 5".

4.2 Rehash message

      Command: REHASH
   Parameters: None

   The rehash command is an administrative command which can be used by
   an operator to force the server to re-read and process its
   configuration file.

   Numeric Replies:

           RPL_REHASHING                 ERR_NOPRIVILEGES


   Example:

   REHASH                          ; message from user with operator
                                   status to server asking it to reread
                                   its configuration file.

4.3 Die message

      Command: DIE
   Parameters: None

   An operator can use the DIE command to shutdown the server.  This
   message is optional since it may be viewed as a risk to allow
   arbitrary people to connect to a server as an operator and execute
   this command.

   The DIE command MUST always be fully processed by the server to which
   the sending client is connected and MUST NOT be passed onto other
   connected servers.

   Numeric Replies:

           ERR_NOPRIVILEGES

   Example:

   DIE                             ; no parameters required.

4.4 Restart message

      Command: RESTART
   Parameters: None

   An operator can use the restart command to force the server to
   restart itself.  This message is optional since it may be viewed as a
   risk to allow arbitrary people to connect to a server as an operator
   and execute this command, causing (at least) a disruption to service.

   The RESTART command MUST always be fully processed by the server to
   which the sending client is connected and MUST NOT be passed onto
   other connected servers.

   Numeric Replies:

           ERR_NOPRIVILEGES

   Example:

   RESTART                         ; no parameters required.

4.5 Summon message

      Command: SUMMON
   Parameters: <user> [ <target> [ <channel> ] ]

   The SUMMON command can be used to give users who are on a host
   running an IRC server a message asking them to please join IRC.  This
   message is only sent if the target server (a) has SUMMON enabled, (b)
   the user is logged in and (c) the server process can write to the
   user's tty (or similar).

   If no <server> parameter is given it tries to summon <user> from the
   server the client is connected to is assumed as the target.

   If summon is not enabled in a server, it MUST return the
   ERR_SUMMONDISABLED numeric.

   Numeric Replies:

           ERR_NORECIPIENT               ERR_FILEERROR
           ERR_NOLOGIN                   ERR_NOSUCHSERVER
           ERR_SUMMONDISABLED            RPL_SUMMONING

   Examples:

   SUMMON jto                      ; summon user jto on the server's
                                   host

   SUMMON jto tolsun.oulu.fi       ; summon user jto on the host which a
                                   server named "tolsun.oulu.fi" is
                                   running.

4.6 Users

      Command: USERS
   Parameters: [ <target> ]

   The USERS command returns a list of users logged into the server in a
   format similar to the UNIX commands who(1), rusers(1) and finger(1).
   If disabled, the correct numeric MUST be returned to indicate this.

   Because of the security implications of such a command, it SHOULD be
   disabled by default in server implementations.  Enabling it SHOULD
   require recompiling the server or some equivalent change rather than
   simply toggling an option and restarting the server.  The procedure
   to enable this command SHOULD also include suitable large comments.

   Numeric Replies:

           ERR_NOSUCHSERVER              ERR_FILEERROR
           RPL_USERSSTART                RPL_USERS
           RPL_NOUSERS                   RPL_ENDOFUSERS
           ERR_USERSDISABLED

   Disabled Reply:

           ERR_USERSDISABLED

   Example:

   USERS eff.org                   ; request a list of users logged in
                                   on server eff.org

4.7 Operwall message

      Command: WALLOPS
   Parameters: <Text to be sent>

   The WALLOPS command is used to send a message to all currently
   connected users who have set the 'w' user mode for themselves.  (See
   Section 3.1.5 "User modes").

   After implementing WALLOPS as a user command it was found that it was
   often and commonly abused as a means of sending a message to a lot of
   people.  Due to this, it is RECOMMENDED that the implementation of
   WALLOPS allows and recognizes only servers as the originators of
   WALLOPS.

   Numeric Replies:

           ERR_NEEDMOREPARAMS

   Example:

   :csd.bu.edu WALLOPS :Connect '*.uiuc.edu 6667' from Joshua ; WALLOPS
                                   message from csd.bu.edu announcing a
                                   CONNECT message it received from
                                   Joshua and acted upon.

4.8 Userhost message

      Command: USERHOST
   Parameters: <nickname> *( SPACE <nickname> )

   The USERHOST command takes a list of up to 5 nicknames, each
   separated by a space character and returns a list of information
   about each nickname that it found.  The returned list has each reply
   separated by a space.

   Numeric Replies:

           RPL_USERHOST                  ERR_NEEDMOREPARAMS

   Example:

   USERHOST Wiz Michael syrk       ; USERHOST request for information on
                                   nicks "Wiz", "Michael", and "syrk"

   :ircd.stealth.net 302 yournick :syrk=+<EMAIL>
                                   ; Reply for user syrk

4.9 Ison message

      Command: ISON
   Parameters: <nickname> *( SPACE <nickname> )

   The ISON command was implemented to provide a quick and efficient
   means to get a response about whether a given nickname was currently
   on IRC. ISON only takes one (1) type of parameter: a space-separated
   list of nicks.  For each nickname in the list that is present, the

   server adds that to its reply string.  Thus the reply string may
   return empty (none of the given nicks are present), an exact copy of
   the parameter string (all of them present) or any other subset of the
   set of nicks given in the parameter.  The only limit on the number of
   nicks that may be checked is that the combined length MUST NOT be too
   large as to cause the server to chop it off so it fits in 512
   characters.

   ISON is only processed by the server local to the client sending the
   command and thus not passed onto other servers for further
   processing.

   Numeric Replies:

           RPL_ISON                      ERR_NEEDMOREPARAMS

   Example:

   ISON phone trillian WiZ jarlek Avalon Angel Monstah syrk
                                   ; Sample ISON request for 7 nicks.

5. Replies


EID 3255 (Verified) is as follows:

Section: 5. Replies

Original Text:

353    RPL_NAMREPLY
              "( "=" / "*" / "@" ) <channel>
               :[ "@" / "+" ] <nick> *( " " [ "@" / "+" ] <nick> )
         - "@" is used for secret channels, "*" for private
           channels, and "=" for others (public channels).

Corrected Text:

353    RPL_NAMREPLY
              "( "=" / "*" / "@" ) <channel>
               :[ "@" / "+" ] <nick> *( " " [ "@" / "+" ] <nick> )"
         - "@" is used for secret channels, "*" for private
           channels, and "=" for others (public channels).

Notes:

Missing double qoutes to end the reply string.

   The following is a list of numeric replies which are generated in
   response to the commands given above.  Each numeric is given with its
   number, name and reply string.

5.1 Command responses


EID 2822 (Verified) is as follows:

Section: 5.1

Original Text:

The original text is missing.

Corrected Text:

       416    ERR_TOOMANYMATCHES
              "<channel> :Output too long (try locally)"

         - Returned by a server in response to a LIST or NAMES 
           message to indicate the result contains too many
           items to be returned to the client.

Notes:

None

   Numerics in the range from 001 to 099 are used for client-server
   connections only and should never travel between servers.  Replies
   generated in the response to commands are found in the range from 200
   to 399.

       001    RPL_WELCOME
              "Welcome to the Internet Relay Network
               <nick>!<user>@<host>"
       002    RPL_YOURHOST
              "Your host is <servername>, running version <ver>"
       003    RPL_CREATED
              "This server was created <date>"
       004    RPL_MYINFO
              "<servername> <version> <available user modes>
               <available channel modes>"

         - The server sends Replies 001 to 004 to a user upon
           successful registration.

       005    RPL_BOUNCE
              "Try server <server name>, port <port number>"

         - Sent by the server to a user to suggest an alternative
           server.  This is often used when the connection is
           refused because the server is already full.

       302    RPL_USERHOST
              ":*1<reply> *( " " <reply> )"

         - Reply format used by USERHOST to list replies to
           the query list.  The reply string is composed as
           follows:

           reply = nickname [ "*" ] "=" ( "+" / "-" ) hostname

           The '*' indicates whether the client has registered
           as an Operator.  The '-' or '+' characters represent
           whether the client has set an AWAY message or not
           respectively.

       303    RPL_ISON
              ":*1<nick> *( " " <nick> )"

         - Reply format used by ISON to list replies to the
           query list.

       301    RPL_AWAY
              "<nick> :<away message>"
       305    RPL_UNAWAY
              ":You are no longer marked as being away"
       306    RPL_NOWAWAY
              ":You have been marked as being away"

         - These replies are used with the AWAY command (if
           allowed).  RPL_AWAY is sent to any client sending a
           PRIVMSG to a client which is away.  RPL_AWAY is only
           sent by the server to which the client is connected.
           Replies RPL_UNAWAY and RPL_NOWAWAY are sent when the
           client removes and sets an AWAY message.

       311    RPL_WHOISUSER
              "<nick> <user> <host> * :<real name>"
       312    RPL_WHOISSERVER
              "<nick> <server> :<server info>"
       313    RPL_WHOISOPERATOR
              "<nick> :is an IRC operator"

       317    RPL_WHOISIDLE
              "<nick> <integer> :seconds idle"
       318    RPL_ENDOFWHOIS
              "<nick> :End of WHOIS list"
       319    RPL_WHOISCHANNELS
              "<nick> :*( ( "@" / "+" ) <channel> " " )"

         - Replies 311 - 313, 317 - 319 are all replies
           generated in response to a WHOIS message.  Given that
           there are enough parameters present, the answering
           server MUST either formulate a reply out of the above
           numerics (if the query nick is found) or return an
           error reply.  The '*' in RPL_WHOISUSER is there as
           the literal character and not as a wild card.  For
           each reply set, only RPL_WHOISCHANNELS may appear
           more than once (for long lists of channel names).
           The '@' and '+' characters next to the channel name
           indicate whether a client is a channel operator or
           has been granted permission to speak on a moderated
           channel.  The RPL_ENDOFWHOIS reply is used to mark
           the end of processing a WHOIS message.

       314    RPL_WHOWASUSER
              "<nick> <user> <host> * :<real name>"
       369    RPL_ENDOFWHOWAS
              "<nick> :End of WHOWAS"

         - When replying to a WHOWAS message, a server MUST use
           the replies RPL_WHOWASUSER, RPL_WHOISSERVER or
           ERR_WASNOSUCHNICK for each nickname in the presented
           list.  At the end of all reply batches, there MUST
           be RPL_ENDOFWHOWAS (even if there was only one reply
           and it was an error).

       321    RPL_LISTSTART
              Obsolete. Not used.

       322    RPL_LIST
              "<channel> <# visible> :<topic>"
       323    RPL_LISTEND
              ":End of LIST"

         - Replies RPL_LIST, RPL_LISTEND mark the actual replies
           with data and end of the server's response to a LIST
           command.  If there are no channels available to return,
           only the end reply MUST be sent.

       325    RPL_UNIQOPIS
              "<channel> <nickname>"

       324    RPL_CHANNELMODEIS
              "<channel> <mode> <mode params>"

       331    RPL_NOTOPIC
              "<channel> :No topic is set"
       332    RPL_TOPIC
              "<channel> :<topic>"

         - When sending a TOPIC message to determine the
           channel topic, one of two replies is sent.  If
           the topic is set, RPL_TOPIC is sent back else
           RPL_NOTOPIC.

              341    RPL_INVITING Expand
              "<nick> <channel>"

         - Returned by the server to indicate that the
           attempted INVITE message was successful and is
           being passed onto the end client.

       342    RPL_SUMMONING
              "<user> :Summoning user to IRC"

         - Returned by a server answering a SUMMON message to
           indicate that it is summoning that user.

       346    RPL_INVITELIST
              "<channel> <invitemask>"
       347    RPL_ENDOFINVITELIST
              "<channel> :End of channel invite list"

         - When listing the 'invitations masks' for a given channel,
           a server is required to send the list back using the
           RPL_INVITELIST and RPL_ENDOFINVITELIST messages.  A
           separate RPL_INVITELIST is sent for each active mask.
           After the masks have been listed (or if none present) a
           RPL_ENDOFINVITELIST MUST be sent.

       348    RPL_EXCEPTLIST
              "<channel> <exceptionmask>"
       349    RPL_ENDOFEXCEPTLIST
              "<channel> :End of channel exception list"

         - When listing the 'exception masks' for a given channel,
           a server is required to send the list back using the
           RPL_EXCEPTLIST and RPL_ENDOFEXCEPTLIST messages.  A
           separate RPL_EXCEPTLIST is sent for each active mask.
           After the masks have been listed (or if none present)
           a RPL_ENDOFEXCEPTLIST MUST be sent.

       351    RPL_VERSION
              "<version>.<debuglevel> <server> :<comments>"

         - Reply by the server showing its version details.
           The <version> is the version of the software being
           used (including any patchlevel revisions) and the
           <debuglevel> is used to indicate if the server is
           running in "debug mode".

           The "comments" field may contain any comments about
           the version or further version details.

              352    RPL_WHOREPLY Expand
              "<channel> <user> <host> <server> <nick>
              ( "H" / "G" ) ["*"] [ ( "@" / "+" ) ]
              :<hopcount> <real name>"

       315    RPL_ENDOFWHO
              "<name> :End of WHO list"

         - The RPL_WHOREPLY and RPL_ENDOFWHO pair are used
           to answer a WHO message.  The RPL_WHOREPLY is only
           sent if there is an appropriate match to the WHO
           query.  If there is a list of parameters supplied
           with a WHO message, a RPL_ENDOFWHO MUST be sent
           after processing each list item with <name> being
           the item.

       353    RPL_NAMREPLY
              "( "=" / "*" / "@" ) <channel>
               :[ "@" / "+" ] <nick> *( " " [ "@" / "+" ] <nick> )
         - "@" is used for secret channels, "*" for private
           channels, and "=" for others (public channels).

       366    RPL_ENDOFNAMES
              "<channel> :End of NAMES list"

         - To reply to a NAMES message, a reply pair consisting
           of RPL_NAMREPLY and RPL_ENDOFNAMES is sent by the
           server back to the client.  If there is no channel
           found as in the query, then only RPL_ENDOFNAMES is

                      returned.  The exception to this is when a NAMES Expand
           message is sent with no parameters and all visible
           channels and contents are sent back in a series of
           RPL_NAMREPLY messages with a RPL_ENDOFNAMES to mark
           the end.

       364    RPL_LINKS
              "<mask> <server> :<hopcount> <server info>"
       365    RPL_ENDOFLINKS
              "<mask> :End of LINKS list"

         - In replying to the LINKS message, a server MUST send
           replies back using the RPL_LINKS numeric and mark the
           end of the list using an RPL_ENDOFLINKS reply.

       367    RPL_BANLIST
              "<channel> <banmask>"
       368    RPL_ENDOFBANLIST
              "<channel> :End of channel ban list"

         - When listing the active 'bans' for a given channel,
           a server is required to send the list back using the
           RPL_BANLIST and RPL_ENDOFBANLIST messages.  A separate
           RPL_BANLIST is sent for each active banmask.  After the
           banmasks have been listed (or if none present) a
           RPL_ENDOFBANLIST MUST be sent.

       371    RPL_INFO
              ":<string>"
       374    RPL_ENDOFINFO
              ":End of INFO list"

         - A server responding to an INFO message is required to
           send all its 'info' in a series of RPL_INFO messages
           with a RPL_ENDOFINFO reply to indicate the end of the
           replies.

       375    RPL_MOTDSTART
              ":- <server> Message of the day - "
       372    RPL_MOTD
              ":- <text>"
       376    RPL_ENDOFMOTD
              ":End of MOTD command"

         - When responding to the MOTD message and the MOTD file
           is found, the file is displayed line by line, with
           each line no longer than 80 characters, using

           RPL_MOTD format replies.  These MUST be surrounded
           by a RPL_MOTDSTART (before the RPL_MOTDs) and an
           RPL_ENDOFMOTD (after).

       381    RPL_YOUREOPER
              ":You are now an IRC operator"

         - RPL_YOUREOPER is sent back to a client which has
           just successfully issued an OPER message and gained
           operator status.

       382    RPL_REHASHING
              "<config file> :Rehashing"

         - If the REHASH option is used and an operator sends
           a REHASH message, an RPL_REHASHING is sent back to
           the operator.

       383    RPL_YOURESERVICE
              "You are service <servicename>"

         - Sent by the server to a service upon successful
           registration.

       391    RPL_TIME
              "<server> :<string showing server's local time>"

         - When replying to the TIME message, a server MUST send
           the reply using the RPL_TIME format above.  The string
           showing the time need only contain the correct day and
           time there.  There is no further requirement for the
           time string.

       392    RPL_USERSSTART
              ":UserID   Terminal  Host"
       393    RPL_USERS
              ":<username> <ttyline> <hostname>"
       394    RPL_ENDOFUSERS
              ":End of users"
       395    RPL_NOUSERS
              ":Nobody logged in"

         - If the USERS message is handled by a server, the
           replies RPL_USERSTART, RPL_USERS, RPL_ENDOFUSERS and
           RPL_NOUSERS are used.  RPL_USERSSTART MUST be sent
           first, following by either a sequence of RPL_USERS
           or a single RPL_NOUSER.  Following this is
           RPL_ENDOFUSERS.

       200    RPL_TRACELINK
              "Link <version & debug level> <destination>
               <next server> V<protocol version>
               <link uptime in seconds> <backstream sendq>
               <upstream sendq>"
       201    RPL_TRACECONNECTING
              "Try. <class> <server>"
       202    RPL_TRACEHANDSHAKE
              "H.S. <class> <server>"
       203    RPL_TRACEUNKNOWN
              "???? <class> [<client IP address in dot form>]"
       204    RPL_TRACEOPERATOR
              "Oper <class> <nick>"
       205    RPL_TRACEUSER
              "User <class> <nick>"
       206    RPL_TRACESERVER
              "Serv <class> <int>S <int>C <server>
               <nick!user|*!*>@<host|server> V<protocol version>"
       207    RPL_TRACESERVICE
              "Service <class> <name> <type> <active type>"
       208    RPL_TRACENEWTYPE
              "<newtype> 0 <client name>"
       209    RPL_TRACECLASS
              "Class <class> <count>"
       210    RPL_TRACERECONNECT
              Unused.
       261    RPL_TRACELOG
              "File <logfile> <debug level>"
       262    RPL_TRACEEND
              "<server name> <version & debug level> :End of TRACE"

         - The RPL_TRACE* are all returned by the server in
           response to the TRACE message.  How many are
           returned is dependent on the TRACE message and
           whether it was sent by an operator or not.  There
           is no predefined order for which occurs first.
           Replies RPL_TRACEUNKNOWN, RPL_TRACECONNECTING and
           RPL_TRACEHANDSHAKE are all used for connections
           which have not been fully established and are either
           unknown, still attempting to connect or in the
           process of completing the 'server handshake'.
           RPL_TRACELINK is sent by any server which handles
           a TRACE message and has to pass it on to another
           server.  The list of RPL_TRACELINKs sent in
           response to a TRACE command traversing the IRC
           network should reflect the actual connectivity of
           the servers themselves along that path.

           RPL_TRACENEWTYPE is to be used for any connection
           which does not fit in the other categories but is
           being displayed anyway.
           RPL_TRACEEND is sent to indicate the end of the list.

       211    RPL_STATSLINKINFO
              "<linkname> <sendq> <sent messages>
               <sent Kbytes> <received messages>
               <received Kbytes> <time open>"

         - reports statistics on a connection.  <linkname>
           identifies the particular connection, <sendq> is
           the amount of data that is queued and waiting to be
           sent <sent messages> the number of messages sent,
           and <sent Kbytes> the amount of data sent, in
           Kbytes. <received messages> and <received Kbytes>
           are the equivalent of <sent messages> and <sent
           Kbytes> for received data, respectively.  <time
           open> indicates how long ago the connection was
           opened, in seconds.

       212    RPL_STATSCOMMANDS
              "<command> <count> <byte count> <remote count>"

         - reports statistics on commands usage.

       219    RPL_ENDOFSTATS
              "<stats letter> :End of STATS report"

       242    RPL_STATSUPTIME
              ":Server Up %d days %d:%02d:%02d"

         - reports the server uptime.

       243    RPL_STATSOLINE
              "O <hostmask> * <name>"

         - reports the allowed hosts from where user may become IRC
           operators.

       221    RPL_UMODEIS
              "<user mode string>"

         - To answer a query about a client's own mode,
           RPL_UMODEIS is sent back.

       234    RPL_SERVLIST
              "<name> <server> <mask> <type> <hopcount> <info>"

       235    RPL_SERVLISTEND
              "<mask> <type> :End of service listing"

         - When listing services in reply to a SERVLIST message,
           a server is required to send the list back using the
           RPL_SERVLIST and RPL_SERVLISTEND messages.  A separate
           RPL_SERVLIST is sent for each service.  After the
           services have been listed (or if none present) a
           RPL_SERVLISTEND MUST be sent.

       251    RPL_LUSERCLIENT
              ":There are <integer> users and <integer>
               services on <integer> servers"
       252    RPL_LUSEROP
              "<integer> :operator(s) online"
       253    RPL_LUSERUNKNOWN
              "<integer> :unknown connection(s)"
       254    RPL_LUSERCHANNELS
              "<integer> :channels formed"
       255    RPL_LUSERME
              ":I have <integer> clients and <integer>
                servers"

         - In processing an LUSERS message, the server
           sends a set of replies from RPL_LUSERCLIENT,
           RPL_LUSEROP, RPL_USERUNKNOWN,
           RPL_LUSERCHANNELS and RPL_LUSERME.  When
           replying, a server MUST send back
           RPL_LUSERCLIENT and RPL_LUSERME.  The other
           replies are only sent back if a non-zero count
           is found for them.

       256    RPL_ADMINME
              "<server> :Administrative info"
       257    RPL_ADMINLOC1
              ":<admin info>"
       258    RPL_ADMINLOC2
              ":<admin info>"
       259    RPL_ADMINEMAIL
              ":<admin info>"

         - When replying to an ADMIN message, a server
           is expected to use replies RPL_ADMINME
           through to RPL_ADMINEMAIL and provide a text
           message with each.  For RPL_ADMINLOC1 a
           description of what city, state and country
           the server is in is expected, followed by
           details of the institution (RPL_ADMINLOC2)

           and finally the administrative contact for the
           server (an email address here is REQUIRED)
           in RPL_ADMINEMAIL.

       263    RPL_TRYAGAIN
              "<command> :Please wait a while and try again."

         - When a server drops a command without processing it,
           it MUST use the reply RPL_TRYAGAIN to inform the
           originating client.

5.2 Error Replies

       Error replies are found in the range from 400 to 599.

       401    ERR_NOSUCHNICK
              "<nickname> :No such nick/channel"

          - Used to indicate the nickname parameter supplied to a
            command is currently unused.

       402    ERR_NOSUCHSERVER
              "<server name> :No such server"

         - Used to indicate the server name given currently
           does not exist.

       403    ERR_NOSUCHCHANNEL
              "<channel name> :No such channel"

         - Used to indicate the given channel name is invalid.

       404    ERR_CANNOTSENDTOCHAN
              "<channel name> :Cannot send to channel"

         - Sent to a user who is either (a) not on a channel
           which is mode +n or (b) not a chanop (or mode +v) on
           a channel which has mode +m set or where the user is
           banned and is trying to send a PRIVMSG message to
           that channel.

       405    ERR_TOOMANYCHANNELS
              "<channel name> :You have joined too many channels"

         - Sent to a user when they have joined the maximum
           number of allowed channels and they try to join
           another channel.

       406    ERR_WASNOSUCHNICK
              "<nickname> :There was no such nickname"

         - Returned by WHOWAS to indicate there is no history
           information for that nickname.

       407    ERR_TOOMANYTARGETS
              "<target> :<error code> recipients. <abort message>"

         - Returned to a client which is attempting to send a
           PRIVMSG/NOTICE using the user@host destination format
           and for a user@host which has several occurrences.

         - Returned to a client which trying to send a
           PRIVMSG/NOTICE to too many recipients.

         - Returned to a client which is attempting to JOIN a safe
           channel using the shortname when there are more than one
           such channel.

       408    ERR_NOSUCHSERVICE
              "<service name> :No such service"

         - Returned to a client which is attempting to send a SQUERY
           to a service which does not exist.

       409    ERR_NOORIGIN
              ":No origin specified"

         - PING or PONG message missing the originator parameter.

       411    ERR_NORECIPIENT
              ":No recipient given (<command>)"
       412    ERR_NOTEXTTOSEND
              ":No text to send"
       413    ERR_NOTOPLEVEL
              "<mask> :No toplevel domain specified"
       414    ERR_WILDTOPLEVEL
              "<mask> :Wildcard in toplevel domain"
       415    ERR_BADMASK
              "<mask> :Bad Server/host mask"

         - 412 - 415 are returned by PRIVMSG to indicate that
           the message wasn't delivered for some reason.
           ERR_NOTOPLEVEL and ERR_WILDTOPLEVEL are errors that
           are returned when an invalid use of
           "PRIVMSG $<server>" or "PRIVMSG #<host>" is attempted.

       421    ERR_UNKNOWNCOMMAND
              "<command> :Unknown command"

         - Returned to a registered client to indicate that the
           command sent is unknown by the server.

       422    ERR_NOMOTD
              ":MOTD File is missing"

         - Server's MOTD file could not be opened by the server.

       423    ERR_NOADMININFO
              "<server> :No administrative info available"

         - Returned by a server in response to an ADMIN message
           when there is an error in finding the appropriate
           information.

       424    ERR_FILEERROR
              ":File error doing <file op> on <file>"

         - Generic error message used to report a failed file
           operation during the processing of a message.

       431    ERR_NONICKNAMEGIVEN
              ":No nickname given"

         - Returned when a nickname parameter expected for a
           command and isn't found.

       432    ERR_ERRONEUSNICKNAME
              "<nick> :Erroneous nickname"

         - Returned after receiving a NICK message which contains
           characters which do not fall in the defined set.  See
           section 2.3.1 for details on valid nicknames.

       433    ERR_NICKNAMEINUSE
              "<nick> :Nickname is already in use"

         - Returned when a NICK message is processed that results
           in an attempt to change to a currently existing
           nickname.

       436    ERR_NICKCOLLISION
              "<nick> :Nickname collision KILL from <user>@<host>"

         - Returned by a server to a client when it detects a
           nickname collision (registered of a NICK that
           already exists by another server).

       437    ERR_UNAVAILRESOURCE
              "<nick/channel> :Nick/channel is temporarily unavailable"

         - Returned by a server to a user trying to join a channel
           currently blocked by the channel delay mechanism.

         - Returned by a server to a user trying to change nickname
           when the desired nickname is blocked by the nick delay
           mechanism.

       441    ERR_USERNOTINCHANNEL
              "<nick> <channel> :They aren't on that channel"

         - Returned by the server to indicate that the target
           user of the command is not on the given channel.

       442    ERR_NOTONCHANNEL
              "<channel> :You're not on that channel"

         - Returned by the server whenever a client tries to
           perform a channel affecting command for which the
           client isn't a member.

       443    ERR_USERONCHANNEL
              "<user> <channel> :is already on channel"

         - Returned when a client tries to invite a user to a
           channel they are already on.

       444    ERR_NOLOGIN
              "<user> :User not logged in"

         - Returned by the summon after a SUMMON command for a
           user was unable to be performed since they were not
           logged in.

       445    ERR_SUMMONDISABLED
              ":SUMMON has been disabled"

         - Returned as a response to the SUMMON command.  MUST be
           returned by any server which doesn't implement it.

       446    ERR_USERSDISABLED
              ":USERS has been disabled"

         - Returned as a response to the USERS command.  MUST be
           returned by any server which does not implement it.

       451    ERR_NOTREGISTERED
              ":You have not registered"

         - Returned by the server to indicate that the client
           MUST be registered before the server will allow it
           to be parsed in detail.

       461    ERR_NEEDMOREPARAMS
              "<command> :Not enough parameters"

         - Returned by the server by numerous commands to
           indicate to the client that it didn't supply enough
           parameters.

       462    ERR_ALREADYREGISTRED
              ":Unauthorized command (already registered)"

         - Returned by the server to any link which tries to
           change part of the registered details (such as
           password or user details from second USER message).

       463    ERR_NOPERMFORHOST
              ":Your host isn't among the privileged"

         - Returned to a client which attempts to register with
           a server which does not been setup to allow
           connections from the host the attempted connection
           is tried.

       464    ERR_PASSWDMISMATCH
              ":Password incorrect"

         - Returned to indicate a failed attempt at registering
           a connection for which a password was required and
           was either not given or incorrect.

       465    ERR_YOUREBANNEDCREEP
              ":You are banned from this server"

         - Returned after an attempt to connect and register
           yourself with a server which has been setup to
           explicitly deny connections to you.

       466    ERR_YOUWILLBEBANNED

         - Sent by a server to a user to inform that access to the
           server will soon be denied.

       467    ERR_KEYSET
              "<channel> :Channel key already set"
       471    ERR_CHANNELISFULL
              "<channel> :Cannot join channel (+l)"
       472    ERR_UNKNOWNMODE
              "<char> :is unknown mode char to me for <channel>"
       473    ERR_INVITEONLYCHAN
              "<channel> :Cannot join channel (+i)"
       474    ERR_BANNEDFROMCHAN
              "<channel> :Cannot join channel (+b)"
       475    ERR_BADCHANNELKEY
              "<channel> :Cannot join channel (+k)"
       476    ERR_BADCHANMASK
              "<channel> :Bad Channel Mask"
       477    ERR_NOCHANMODES
              "<channel> :Channel doesn't support modes"
       478    ERR_BANLISTFULL
              "<channel> <char> :Channel list is full"

       481    ERR_NOPRIVILEGES
              ":Permission Denied- You're not an IRC operator"

         - Any command requiring operator privileges to operate
           MUST return this error to indicate the attempt was
           unsuccessful.

       482    ERR_CHANOPRIVSNEEDED
              "<channel> :You're not channel operator"

         - Any command requiring 'chanop' privileges (such as
           MODE messages) MUST return this error if the client
           making the attempt is not a chanop on the specified
           channel.

       483    ERR_CANTKILLSERVER
              ":You can't kill a server!"

         - Any attempts to use the KILL command on a server
           are to be refused and this error returned directly
           to the client.

       484    ERR_RESTRICTED
              ":Your connection is restricted!"

         - Sent by the server to a user upon connection to indicate
           the restricted nature of the connection (user mode "+r").

       485    ERR_UNIQOPPRIVSNEEDED
              ":You're not the original channel operator"

         - Any MODE requiring "channel creator" privileges MUST
           return this error if the client making the attempt is not
           a chanop on the specified channel.

       491    ERR_NOOPERHOST
              ":No O-lines for your host"

         - If a client sends an OPER message and the server has
           not been configured to allow connections from the
           client's host as an operator, this error MUST be
           returned.

       501    ERR_UMODEUNKNOWNFLAG
              ":Unknown MODE flag"

         - Returned by the server to indicate that a MODE
           message was sent with a nickname parameter and that
           the a mode flag sent was not recognized.

       502    ERR_USERSDONTMATCH
              ":Cannot change mode for other users"

         - Error sent to any user trying to view or change the
           user mode for a user other than themselves.

5.3 Reserved numerics

   These numerics are not described above since they fall into one of
   the following categories:

   1. no longer in use;

   2. reserved for future planned use;

   3. in current use but are part of a non-generic 'feature' of
      the current IRC server.

            231    RPL_SERVICEINFO     232  RPL_ENDOFSERVICES
            233    RPL_SERVICE
            300    RPL_NONE            316  RPL_WHOISCHANOP
            361    RPL_KILLDONE        362  RPL_CLOSING
            363    RPL_CLOSEEND        373  RPL_INFOSTART
            384    RPL_MYPORTIS

            213    RPL_STATSCLINE      214  RPL_STATSNLINE
            215    RPL_STATSILINE      216  RPL_STATSKLINE
            217    RPL_STATSQLINE      218  RPL_STATSYLINE
            240    RPL_STATSVLINE      241  RPL_STATSLLINE
               244    RPL_STATSHLINE      245  RPL_STATSSLINE Expand

            246    RPL_STATSPING       247  RPL_STATSBLINE
            250    RPL_STATSDLINE

            492    ERR_NOSERVICEHOST

6. Current implementations

   The IRC software, version 2.10 is the only complete implementation of
   the IRC protocol (client and server).  Because of the small amount of
   changes in the client protocol since the publication of RFC 1459
   [IRC], implementations that follow it are likely to be compliant with
   this protocol or to require a small amount of changes to reach
   compliance.

7. Current problems

   There are a number of recognized problems with the IRC Client
   Protocol, and more generally with the IRC Server Protocol.  In order
   to preserve backward compatibility with old clients, this protocol
   has almost not evolved since the publication of RFC 1459 [IRC].

7.1 Nicknames

   The idea of the nickname on IRC is very convenient for users to use
   when talking to each other outside of a channel, but there is only a
   finite nickname space and being what they are, it's not uncommon for
   several people to want to use the same nick.  If a nickname is chosen
   by two people using this protocol, either one will not succeed or
   both will removed by use of a server KILL (See Section 3.7.1).

7.2 Limitation of wildcards

   There is no way to escape the escape character "\" (%x5C).  While
   this isn't usually a problem, it makes it impossible to form a mask
   with a backslash character ("\") preceding a wildcard.

7.3 Security considerations

   Security issues related to this protocol are discussed in the "IRC
   Server Protocol" [IRC-SERVER] as they are mostly an issue for the
   server side of the connection.

8. Current support and availability

        Mailing lists for IRC related discussion:
          General discussion: <EMAIL>
          Protocol development: <EMAIL>

        Software implementations:
          ftp://ftp.irc.org/irc/server
          ftp://ftp.funet.fi/pub/unix/irc
          ftp://ftp.irc.org/irc/clients

        Newsgroup: alt.irc

9. Acknowledgements

   Parts of this document were copied from the RFC 1459 [IRC] which
   first formally documented the IRC Protocol.  It has also benefited
   from many rounds of review and comments.  In particular, the
   following people have made significant contributions to this
   document:

   Matthew Green, Michael Neumayer, Volker Paulsen, Kurt Roeckx, Vesa
   Ruokonen, Magnus Tjernstrom, Stefan Zehl.

10. References

   [KEYWORDS]   Bradner, S., "Key words for use in RFCs to Indicate
                Requirement Levels", BCP 14, RFC 2119, March 1997.

   [ABNF]       Crocker, D. and P. Overell, "Augmented BNF for Syntax
                Specifications: ABNF", RFC 2234, November 1997.

   [HNAME]      Braden, R., "Requirements for Internet Hosts --
                Application and Support", STD 3, RFC 1123, October 1989.

   [IRC]        Oikarinen, J. & D. Reed, "Internet Relay Chat Protocol",
                RFC 1459, May 1993.

   [IRC-ARCH]   Kalt, C., "Internet Relay Chat: Architecture", RFC 2810,
                April 2000.

   [IRC-CHAN]   Kalt, C., "Internet Relay Chat: Channel Management", RFC
                2811, April 2000.

   [IRC-SERVER] Kalt, C., "Internet Relay Chat: Server Protocol", RFC
                2813, April 2000.

11. Author's Address

   Christophe Kalt
   99 Teaneck Rd, Apt #117
   Ridgefield Park, NJ 07660
   USA

   EMail: <EMAIL>

12.  Full Copyright Statement

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

   This document and translations of it may be copied and furnished to
   others, and derivative works that comment on or otherwise explain it
   or assist in its implementation may be prepared, copied, published
   and distributed, in whole or in part, without restriction of any
   kind, provided that the above copyright notice and this paragraph are
   included on all such copies and derivative works.  However, this
   document itself may not be modified in any way, such as by removing
   the copyright notice or references to the Internet Society or other
   Internet organizations, except as needed for the purpose of
   developing Internet standards in which case the procedures for
   copyrights defined in the Internet Standards process must be
   followed, or as required to translate it into languages other than
   English.

   The limited permissions granted above are perpetual and will not be
   revoked by the Internet Society or its successors or assigns.

   This document and the information contained herein is provided on an
   "AS IS" basis and THE INTERNET SOCIETY AND THE INTERNET ENGINEERING
   TASK FORCE DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING
   BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION
   HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF
   MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

Acknowledgement

   Funding for the RFC Editor function is currently provided by the
   Internet Society.

#### RFC 2813 Internet Relay Chat: Server Protool

Network Working Group                                           C. Kalt
Request for Comments: 2813                                   April 2000
Updates: 1459
Category: Informational


                  Internet Relay Chat: Server Protocol

Status of this Memo

   This memo provides information for the Internet community.  It does
   not specify an Internet standard of any kind.  Distribution of this
   memo is unlimited.

Copyright Notice

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

Abstract

   While based on the client-server model, the IRC (Internet Relay Chat)
   protocol allows servers to connect to each other effectively forming
   a network.

   This document defines the protocol used by servers to talk to each
   other.  It was originally a superset of the client protocol but has
   evolved differently.

   First formally documented in May 1993 as part of RFC 1459 [IRC], most
   of the changes brought since then can be found in this document as
   development was focused on making the protocol scale better.  Better
   scalability has allowed existing world-wide networks to keep growing
   and reach sizes which defy the old specification.

Table of Contents

   1.  Introduction ...............................................   3
   2.  Global database ............................................   3
      2.1  Servers ................................................   3
      2.2  Clients ................................................   4
         2.2.1  Users .............................................   4
         2.2.2  Services ..........................................   4
      2.3  Channels ...............................................   4
   3.  The IRC Server Specification ...............................   5
      3.1  Overview ...............................................   5
      3.2  Character codes ........................................   5
      3.3  Messages ...............................................   5
         3.3.1  Message format in Augmented BNF ...................   6
      3.4  Numeric replies ........................................   7
   4.  Message Details ............................................   7
      4.1  Connection Registration ................................   8
         4.1.1  Password message ..................................   8
         4.1.2  Server message ....................................   9
         4.1.3  Nick ..............................................  10
         4.1.4  Service message ...................................  11
         4.1.5  Quit ..............................................  12
         4.1.6  Server quit message ...............................  13
      4.2  Channel operations .....................................  14
         4.2.1  Join message ......................................  14
         4.2.2  Njoin message .....................................  15
         4.2.3  Mode message ......................................  16
   5.  Implementation details  ....................................  16
      5.1  Connection 'Liveness' ..................................  16
      5.2  Accepting a client to server connection ................  16
         5.2.1  Users .............................................  16
         5.2.2  Services ..........................................  17
      5.3  Establishing a server-server connection. ...............  17
         5.3.1  Link options ......................................  17
            5.3.1.1  Compressed server to server links ............  18
            *******  Anti abuse protections .......................  18
         5.3.2  State information exchange when connecting ........  18
      5.4  Terminating server-client connections ..................  19
      5.5  Terminating server-server connections ..................  19
      5.6  Tracking nickname changes ..............................  19
      5.7  Tracking recently used nicknames .......................  20
      5.8  Flood control of clients ...............................  20
      5.9  Non-blocking lookups ...................................  21
         5.9.1  Hostname (DNS) lookups ............................  21
         5.9.2  Username (Ident) lookups ..........................  21
   6.  Current problems ...........................................  21
      6.1  Scalability ............................................  21
      6.2  Labels .................................................  22

         6.2.1  Nicknames .........................................  22
         6.2.2  Channels ..........................................  22
         6.2.3  Servers ...........................................  22
      6.3  Algorithms .............................................  22
   7.  Security Considerations ....................................  23
      7.1  Authentication .........................................  23
      7.2  Integrity ..............................................  23
   8.  Current support and availability ...........................  24
   9.  Acknowledgements ...........................................  24
   10.  References ................................................  24
   11.  Author's Address ..........................................  25
   12. Full Copyright Statement ...................................  26

1. Introduction

   This document is intended for people working on implementing an IRC
   server but will also be useful to anyone implementing an IRC service.

   Servers provide the three basic services required for realtime
   conferencing defined by the "Internet Relay Chat: Architecture"
   [IRC-ARCH]: client locator (via the client protocol [IRC-CLIENT]),
   message relaying (via the server protocol defined in this document)
   and channel hosting and management (following specific rules [IRC-
   CHAN]).

2. Global database

   Although the IRC Protocol defines a fairly distributed model, each
   server maintains a "global state database" about the whole IRC
   network.  This database is, in theory, identical on all servers.

2.1 Servers

   Servers are uniquely identified by their name which has a maximum
   length of sixty three (63) characters.  See the protocol grammar
   rules (section 3.3.1) for what may and may not be used in a server
   name.

   Each server is typically known by all other servers, however it is
   possible to define a "hostmask" to group servers together according
   to their name.  Inside the hostmasked area, all the servers have a
   name which matches the hostmask, and any other server with a name
   matching the hostmask SHALL NOT be connected to the IRC network
   outside the hostmasked area.  Servers which are outside the area have
   no knowledge of the individual servers present inside the area,
   instead they are presented with a virtual server which has the
   hostmask for name.

2.2 Clients

   For each client, all servers MUST have the following information: a
   netwide unique identifier (whose format depends on the type of
   client) and the server to which the client is connected.

2.2.1 Users

   Each user is distinguished from other users by a unique nickname
   having a maximum length of nine (9) characters.  See the protocol
   grammar rules (section 3.3.1) for what may and may not be used in a
   nickname.  In addition to the nickname, all servers MUST have the
   following information about all users: the name of the host that the
   user is running on, the username of the user on that host, and the
   server to which the client is connected.

2.2.2 Services

   Each service is distinguished from other services by a service name
   composed of a nickname and a server name.  The nickname has a maximum
   length of nine (9) characters.  See the protocol grammar rules
   (section 3.3.1) for what may and may not be used in a nickname.  The
   server name used to compose the service name is the name of the
   server to which the service is connected.  In addition to this
   service name all servers MUST know the service type.

   Services differ from users by the format of their identifier, but
   more importantly services and users don't have the same type of
   access to the server: services can request part or all of the global
   state information that a server maintains, but have a more restricted
   set of commands available to them (See "IRC Client Protocol" [IRC-
   CLIENT] for details on which) and are not allowed to join channels.
   Finally services are not usually subject to the "Flood control"
   mechanism described in section 5.8.

2.3 Channels

   Alike services, channels have a scope [IRC-CHAN] and are not
   necessarily known to all servers.  When a channel existence is known
   to a server, the server MUST keep track of the channel members, as
   well as the channel modes.

3. The IRC Server Specification

3.1 Overview

   The protocol as described herein is for use with server to server
   connections.  For client to server connections, see the IRC Client
   Protocol specification.

   There are, however, more restrictions on client connections (which
   are considered to be untrustworthy) than on server connections.

3.2 Character codes

   No specific character set is specified. The protocol is based on a a
   set of codes which are composed of eight (8) bits, making up an
   octet.  Each message may be composed of any number of these octets;
   however, some octet values are used for control codes which act as
   message delimiters.

   Regardless of being an 8-bit protocol, the delimiters and keywords
   are such that protocol is mostly usable from US-ASCII terminal and a
   telnet connection.

   Because of IRC's Scandinavian origin, the characters {}|^ are
   considered to be the lower case equivalents of the characters []\~,
   respectively. This is a critical issue when determining the
   equivalence of two nicknames, or channel names.

3.3 Messages

   Servers and clients send each other messages which may or may not
   generate a reply.  Most communication between servers do not generate
   any reply, as servers mostly perform routing tasks for the clients.

   Each IRC message may consist of up to three main parts: the prefix
   (OPTIONAL), the command, and the command parameters (maximum of
   fifteen (15)).  The prefix, command, and all parameters are separated
   by one ASCII space character (0x20) each.

      The presence of a prefix is indicated with a single leading ASCII Expand
   colon character (':', 0x3a), which MUST be the first character of the
   message itself.  There MUST be NO gap (whitespace) between the colon
   and the prefix.  The prefix is used by servers to indicate the true
   origin of the message.  If the prefix is missing from the message, it
   is assumed to have originated from the connection from which it was
   received.  Clients SHOULD not use a prefix when sending a message
   from themselves; if they use one, the only valid prefix is the
   registered nickname associated with the client.

   When a server receives a message, it MUST identify its source using
   the (eventually assumed) prefix.  If the prefix cannot be found in
   the server's internal database, it MUST be discarded, and if the
   prefix indicates the message comes from an (unknown) server, the link
   from which the message was received MUST be dropped.  Dropping a link
   in such circumstances is a little excessive but necessary to maintain
   the integrity of the network and to prevent future problems.  Another
   common error condition is that the prefix found in the server's
   internal database identifies a different source (typically a source
   registered from a different link than from which the message
   arrived).  If the message was received from a server link and the
   prefix identifies a client, a KILL message MUST be issued for the
   client and sent to all servers.  In other cases, the link from which
   the message arrived SHOULD be dropped for clients, and MUST be
   dropped for servers.  In all cases, the message MUST be discarded.

   The command MUST either be a valid IRC command or a three (3) digit
   number represented in ASCII text.

   IRC messages are always lines of characters terminated with a CR-LF
   (Carriage Return - Line Feed) pair, and these messages SHALL NOT
   exceed 512 characters in length, counting all characters including
   the trailing CR-LF. Thus, there are 510 characters maximum allowed
   for the command and its parameters.  There is no provision for
   continuation message lines.  See section 5 for more details about
   current implementations.

3.3.1 Message format in Augmented BNF

   The protocol messages must be extracted from the contiguous stream of
   octets.  The current solution is to designate two characters, CR and
   LF, as message separators.  Empty messages are silently ignored,
   which permits use of the sequence CR-LF between messages without
   extra problems.

   The extracted message is parsed into the components <prefix>,
   <command> and list of parameters (<params>).

   The Augmented BNF representation for this is found in "IRC Client
   Protocol" [IRC-CLIENT].

   The extended prefix (["!" user "@" host ]) MUST NOT be used in server
   to server communications and is only intended for server to client
   messages in order to provide clients with more useful information
   about who a message is from without the need for additional queries.

3.4 Numeric replies

   Most of the messages sent to the server generate a reply of some
   sort.  The most common reply is the numeric reply, used for both
   errors and normal replies.  The numeric reply MUST be sent as one
   message consisting of the sender prefix, the three digit numeric, and
   the target of the reply.  A numeric reply is not allowed to originate
   from a client; any such messages received by a server are silently
   dropped. In all other respects, a numeric reply is just like a normal
   message, except that the keyword is made up of 3 numeric digits
   rather than a string of letters.  A list of different replies is
   supplied in "IRC Client Protocol" [IRC-CLIENT].

4. Message Details

   All the messages recognized by the IRC server and client are
   described in the IRC Client Protocol specification.

   Where the reply ERR_NOSUCHSERVER is returned, it means that the
   target of the message could not be found.  The server MUST NOT send
   any other replies after this error for that command.

   The server to which a client is connected is required to parse the
   complete message, returning any appropriate errors.  If the server
   encounters a fatal error while parsing a message, an error MUST be
   sent back to the client and the parsing terminated.  A fatal error
   may follow from incorrect command, a destination which is otherwise
   unknown to the server (server, client or channel names fit this
   category), not enough parameters or incorrect privileges.

   If a full set of parameters is presented, then each MUST be checked
   for validity and appropriate responses sent back to the client.  In
   the case of messages which use parameter lists using the comma as an
   item separator, a reply MUST be sent for each item.

   In the examples below, some messages appear using the full format:

   :Name COMMAND parameter list

   Such examples represent a message from "Name" in transit between
   servers, where it is essential to include the name of the original
   sender of the message so remote servers may send back a reply along
   the correct path.

   The message details for client to server communication are described
   in the "IRC Client Protocol" [IRC-CLIENT].  Some sections in the
   following pages apply to some of these messages, they are additions
   to the message specifications which are only relevant to server to

   server communication, or to the server implementation.  The messages
   which are introduced here are only used for server to server
   communication.

4.1 Connection Registration

   The commands described here are used to register a connection with
   another IRC server.

4.1.1 Password message

      Command: PASS
   Parameters: <password> <version> <flags> [<options>]

   The PASS command is used to set a 'connection password'.  The
   password MUST be set before any attempt to register the connection is
   made.  Currently this means that servers MUST send a PASS command
   before any SERVER command.  Only one (1) PASS command SHALL be
   accepted from a connection.

   The last three (3) parameters MUST be ignored if received from a
   client (e.g. a user or a service).  They are only relevant when
   received from a server.

   The <version> parameter is a string of at least four (4) characters,
   and up to fourteen (14) characters.  The first four (4) characters
   MUST be digits and indicate the protocol version known by the server
   issuing the message.  The protocol described by this document is
   version 2.10 which is encoded as "0210".  The remaining OPTIONAL
   characters are implementation dependent and should describe the
   software version number.

   The <flags> parameter is a string of up to one hundred (100)
   characters.  It is composed of two substrings separated by the
   character "|" (%x7C).  If present, the first substring MUST be the
   name of the implementation.  The reference implementation (See
   Section 8, "Current support and availability") uses the string "IRC".
   If a different implementation is written, which needs an identifier,
   then that identifier should be registered through publication of an
   RFC. The second substring is implementation dependent.  Both
   substrings are OPTIONAL, but the character "|" is REQUIRED.  The
   character "|" MUST NOT appear in either substring.

   Finally, the last parameter, <options>, is used for link options.
   The only options defined by the protocol are link compression (using
   the character "Z"), and an abuse protection flag (using the character

   "P").  See sections 5.3.1.1 (Compressed server to server links) and
   ******* (Anti abuse protections) respectively for more information on
   these options.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_ALREADYREGISTRED

   Example:

        PASS moresecretpassword 0210010000 IRC|aBgH$ Z

4.1.2 Server message

      Command: SERVER
   Parameters: <servername> <hopcount> <token> <info>

   The SERVER command is used to register a new server. A new connection
   introduces itself as a server to its peer.  This message is also used
   to pass server data over whole net.  When a new server is connected
   to net, information about it MUST be broadcasted to the whole
   network.

   The <info> parameter may contain space characters.

   <hopcount> is used to give all servers some internal information on
   how far away each server is.  Local peers have a value of 0, and each
   passed server increments the value.  With a full server list, it
   would be possible to construct a map of the entire server tree, but
   hostmasks prevent this from being done.

   The <token> parameter is an unsigned number used by servers as an
   identifier.  This identifier is subsequently used to reference a
   server in the NICK and SERVICE messages sent between servers.  Server
   tokens only have a meaning for the point-to-point peering they are
   used and MUST be unique for that connection.  They are not global.

   The SERVER message MUST only be accepted from either (a) a connection
   which is yet to be registered and is attempting to register as a
   server, or (b) an existing connection to another server, in which
   case the SERVER message is introducing a new server behind that
   server.

   Most errors that occur with the receipt of a SERVER command result in
   the connection being terminated by the destination host (target
   SERVER).  Because of the severity of such event, error replies are
   usually sent using the "ERROR" command rather than a numeric.

   If a SERVER message is parsed and it attempts to introduce a server
   which is already known to the receiving server, the connection, from
   which that message arrived, MUST be closed (following the correct
   procedures), since a duplicate route to a server has been formed and
   the acyclic nature of the IRC tree breaks.  In some conditions, the
   connection from which the already known server has registered MAY be
   closed instead.  It should be noted that this kind of error can also
   be the result of a second running server, problem which cannot be
   fixed within the protocol and typically requires human intervention.
   This type of problem is particularly insidious, as it can quite
   easily result in part of the IRC network to be isolated, with one of
   the two servers connected to each partition therefore making it
   impossible for the two parts to unite.

   Numeric Replies:

           ERR_ALREADYREGISTRED

   Example:

   SERVER test.oulu.fi 1 1 :Experimental server ; New server
                                   test.oulu.fi introducing itself and
                                   attempting to register.

   :tolsun.oulu.fi SERVER csd.bu.edu 5 34 :BU Central Server ; Server
                                   tolsun.oulu.fi is our uplink for
                                   csd.bu.edu which is 5 hops away.  The
                                   token "34" will be used by
                                   tolsun.oulu.fi when introducing new
                                   users or services connected to
                                   csd.bu.edu.

4.1.3 Nick

      Command: NICK
   Parameters: <nickname> <hopcount> <username> <host> <servertoken>
               <umode> <realname>

   This form of the NICK message MUST NOT be allowed from user
   connections. However, it MUST be used instead of the NICK/USER pair
   to notify other servers of new users joining the IRC network.

   This message is really the combination of three distinct messages:
   NICK, USER and MODE [IRC-CLIENT].

   The <hopcount> parameter is used by servers to indicate how far away
   a user is from its home server.  A local connection has a hopcount of
   0.  The hopcount value is incremented by each passed server.

   The <servertoken> parameter replaces the <servername> parameter of
   the USER (See section 4.1.2 for more information on server tokens).

   Examples:

   NICK syrk 5 kalt millennium.stealth.net 34 +i :Christophe Kalt ; New
                                   user with nickname "syrk", username
                                   "kalt", connected from host
                                   "millennium.stealth.net" to server
                                   "34" ("csd.bu.edu" according to the
                                   previous example).

   :krys NICK syrk                 ; The other form of the NICK message,
                                   as defined in "IRC Client Protocol"
                                   [IRC-CLIENT] and used between
                                   servers: krys changed his nickname to
                                   syrk

4.1.4 Service message

      Command: SERVICE
   Parameters: <servicename> <servertoken> <distribution> <type>
                <hopcount> <info>

   The SERVICE command is used to introduce a new service.  This form of
   the SERVICE message SHOULD NOT be allowed from client (unregistered,
   or registered) connections.  However, it MUST be used between servers
   to notify other servers of new services joining the IRC network.

   The <servertoken> is used to identify the server to which the service
   is connected.  (See section 4.1.2 for more information on server
   tokens).

   The <hopcount> parameter is used by servers to indicate how far away
   a service is from its home server.  A local connection has a hopcount
   of 0.  The hopcount value is incremented by each passed server.

   The <distribution> parameter is used to specify the visibility of a
   service.  The service may only be known to servers which have a name
   matching the distribution.  For a matching server to have knowledge
   of the service, the network path between that server and the server
   to which the service is connected MUST be composed of servers whose
   names all match the mask.  Plain "*" is used when no restriction is
   wished.

   The <type> parameter is currently reserved for future usage.

   Numeric Replies:

           ERR_ALREADYREGISTRED            ERR_NEEDMOREPARAMS
           ERR_ERRONEUSNICKNAME
           RPL_YOURESERVICE                RPL_YOURHOST
           RPL_MYINFO

   Example:

SERVICE <EMAIL> 9 *.fr 0 1 :French Dictionary r" registered on
                                   server "9" is being announced to
                                   another server.  This service will
                                   only be available on servers whose
                                   name matches "*.fr".

4.1.5 Quit

      Command: QUIT
   Parameters: [<Quit Message>]

   A client session ends with a quit message.  The server MUST close the
   connection to a client which sends a QUIT message. If a "Quit
   Message" is given, this will be sent instead of the default message,
   the nickname or service name.

   When "netsplit" (See Section 4.1.6) occur, the "Quit Message" is
   composed of the names of two servers involved, separated by a space.
   The first name is that of the server which is still connected and the
   second name is either that of the server which has become
   disconnected or that of the server to which the leaving client was
   connected:

      <Quit Message> =  ":" servername SPACE servername

   Because the "Quit Message" has a special meaning for "netsplits",
   servers SHOULD NOT allow a client to use a <Quit Message> in the
   format described above.

   If, for some other reason, a client connection is closed without the
   client issuing a QUIT command (e.g. client dies and EOF occurs on
   socket), the server is REQUIRED to fill in the quit message with some
   sort of message reflecting the nature of the event which caused it to
   happen.  Typically, this is done by reporting a system specific
   error.

   Numeric Replies:

           None.

   Examples:

   :WiZ QUIT :Gone to have lunch   ; Preferred message format.

4.1.6 Server quit message

      Command: SQUIT
   Parameters: <server> <comment>

   The SQUIT message has two distinct uses.

   The first one (described in "Internet Relay Chat: Client Protocol"
   [IRC-CLIENT]) allows operators to break a local or remote server
   link.  This form of the message is also eventually used by servers to
   break a remote server link.

   The second use of this message is needed to inform other servers when
   a "network split" (also known as "netsplit") occurs, in other words
   to inform other servers about quitting or dead servers.  If a server
   wishes to break the connection to another server it MUST send a SQUIT
   message to the other server, using the name of the other server as
   the server parameter, which then closes its connection to the
   quitting server.

   The <comment> is filled in by servers which SHOULD place an error or
   similar message here.

   Both of the servers which are on either side of the connection being
   closed are REQUIRED to send out a SQUIT message (to all its other
   server connections) for all other servers which are considered to be
   behind that link.

   Similarly, a QUIT message MAY be sent to the other still connected
   servers on behalf of all clients behind that quitting link.  In
   addition to this, all channel members of a channel which lost a
   member due to the "split" MUST be sent a QUIT message.  Messages to
   channel members are generated by each client's local server.

   If a server connection is terminated prematurely (e.g., the server on
   the other end of the link died), the server which detects this
   disconnection is REQUIRED to inform the rest of the network that the
   connection has closed and fill in the comment field with something
   appropriate.

   When a client is removed as the result of a SQUIT message, the server
   SHOULD add the nickname to the list of temporarily unavailable
   nicknames in an attempt to prevent future nickname collisions. See

   section 5.7 (Tracking recently used nicknames) for more information
   on this procedure.

   Numeric replies:

           ERR_NOPRIVILEGES                ERR_NOSUCHSERVER
           ERR_NEEDMOREPARAMS

   Example:

   SQUIT tolsun.oulu.fi :Bad Link ?  ; the server link tolson.oulu.fi
                                   has been terminated because of "Bad
                                   Link".

   :Trillian SQUIT cm22.eng.umd.edu :Server out of control ; message
                                   from Trillian to disconnect
                                   "cm22.eng.umd.edu" from the net
                                   because "Server out of control".

4.2 Channel operations

   This group of messages is concerned with manipulating channels, their
   properties (channel modes), and their contents (typically users).  In
   implementing these, a number of race conditions are inevitable when
   users at opposing ends of a network send commands which will
   ultimately clash.  It is also REQUIRED that servers keep a nickname
   history to ensure that wherever a <nick> parameter is given, the
   server check its history in case it has recently been changed.

4.2.1 Join message

      Command: JOIN
   Parameters: <channel>[ %x7 <modes> ]
               *( "," <channel>[ %x7 <modes> ] )

   The JOIN command is used by client to start listening a specific
   channel. Whether or not a client is allowed to join a channel is
   checked only by the local server the client is connected to; all
   other servers automatically add the user to the channel when the
   command is received from other servers.

   Optionally, the user status (channel modes 'O', 'o', and 'v') on the
   channel may be appended to the channel name using a control G (^G or
   ASCII 7) as separator.  Such data MUST be ignored if the message
   wasn't received from a server.  This format MUST NOT be sent to
   clients, it can only be used between servers and SHOULD be avoided.

   The JOIN command MUST be broadcast to all servers so that each server
   knows where to find the users who are on the channel.  This allows
   optimal delivery of PRIVMSG and NOTICE messages to the channel.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_BANNEDFROMCHAN
           ERR_INVITEONLYCHAN              ERR_BADCHANNELKEY
           ERR_CHANNELISFULL               ERR_BADCHANMASK
           ERR_NOSUCHCHANNEL               ERR_TOOMANYCHANNELS
           ERR_TOOMANYTARGETS              ERR_UNAVAILRESOURCE
           RPL_TOPIC

   Examples:

   :WiZ JOIN #Twilight_zone        ; JOIN message from WiZ

4.2.2 Njoin message

      Command: NJOIN
   Parameters: <channel> [ "@@" / "@" ] [ "+" ] <nickname>
                         *( "," [ "@@" / "@" ] [ "+" ] <nickname> )

   The NJOIN message is used between servers only.  If such a message is
   received from a client, it MUST be ignored.  It is used when two
   servers connect to each other to exchange the list of channel members
   for each channel.

   Even though the same function can be performed by using a succession
   of JOIN, this message SHOULD be used instead as it is more efficient.
   The prefix "@@" indicates that the user is the "channel creator", the
   character "@" alone indicates a "channel operator", and the character
   '+' indicates that the user has the voice privilege.

   Numeric Replies:

           ERR_NEEDMOREPARAMS              ERR_NOSUCHCHANNEL
           ERR_ALREADYREGISTRED

   Examples:

   :ircd.stealth.net NJOIN #Twilight_zone :@WiZ,+syrk,avalon ; NJOIN
                                   message from ircd.stealth.net
                                   announcing users joining the
                                   #Twilight_zone channel: WiZ with
                                   channel operator status, syrk with
                                   voice privilege and avalon with no
                                   privilege.

4.2.3 Mode message

   The MODE message is a dual-purpose command in IRC.  It allows both
   usernames and channels to have their mode changed.

   When parsing MODE messages, it is RECOMMENDED that the entire message
   be parsed first, and then the changes which resulted passed on.

   It is REQUIRED that servers are able to change channel modes so that
   "channel creator" and "channel operators" may be created.

5. Implementation details

   A the time of writing, the only current implementation of this
   protocol is the IRC server, version 2.10. Earlier versions may
   implement some or all of the commands described by this document with
   NOTICE messages replacing many of the numeric replies. Unfortunately,
   due to backward compatibility requirements, the implementation of
   some parts of this document varies with what is laid out.  One
   notable difference is:

        * recognition that any LF or CR anywhere in a message marks
          the end of that message (instead of requiring CR-LF);

   The rest of this section deals with issues that are mostly of
   importance to those who wish to implement a server but some parts
   also apply directly to clients as well.

5.1 Connection 'Liveness'

   To detect when a connection has died or become unresponsive, the
   server MUST poll each of its connections.  The PING command (See "IRC
   Client Protocol" [IRC-CLIENT]) is used if the server doesn't get a
   response from its peer in a given amount of time.

   If a connection doesn't respond in time, its connection is closed
   using the appropriate procedures.

5.2 Accepting a client to server connection

5.2.1 Users

   When a server successfully registers a new user connection, it is
   REQUIRED to send to the user unambiguous messages stating: the user
   identifiers upon which it was registered (RPL_WELCOME), the server
   name and version (RPL_YOURHOST), the server birth information
   (RPL_CREATED), available user and channel modes (RPL_MYINFO), and it
   MAY send any introductory messages which may be deemed appropriate.

   In particular the server SHALL send the current user/service/server
   count (as per the LUSERS reply) and finally the MOTD (if any, as per Expand
   the MOTD reply).

   After dealing with registration, the server MUST then send out to
   other servers the new user's nickname (NICK message), other
   information as supplied by itself (USER message) and as the server
   could discover (from DNS servers).  The server MUST NOT send this
   information out with a pair of NICK and USER messages as defined in
   "IRC Client Protocol" [IRC-CLIENT], but MUST instead take advantage
   of the extended NICK message defined in section 4.1.3.

5.2.2 Services

   Upon successfully registering a new service connection, the server is
   subject to the same kind of REQUIREMENTS as for a user.  Services
   being somewhat different, only the following replies are sent:
   RPL_YOURESERVICE, RPL_YOURHOST, RPL_MYINFO.

   After dealing with this, the server MUST then send out to other
   servers (SERVICE message) the new service's nickname and other
   information as supplied by the service (SERVICE message) and as the
   server could discover (from DNS servers).

5.3 Establishing a server-server connection.

   The process of establishing a server-to-server connection is fraught
   with danger since there are many possible areas where problems can
   occur - the least of which are race conditions.

   After a server has received a connection following by a PASS/SERVER
   pair which were recognized as being valid, the server SHOULD then
   reply with its own PASS/SERVER information for that connection as
   well as all of the other state information it knows about as
   described below.

   When the initiating server receives a PASS/SERVER pair, it too then
   checks that the server responding is authenticated properly before
   accepting the connection to be that server.

5.3.1 Link options

   Server links are based on a common protocol (defined by this
   document) but a particular link MAY set specific options using the
   PASS message (See Section 4.1.1).

5.3.1.1 Compressed server to server links

   If a server wishes to establish a compressed link with its peer, it
   MUST set the 'Z' flag in the options parameter to the PASS message.
   If both servers request compression and both servers are able to
   initialize the two compressed streams, then the remainder of the
   communication is to be compressed.  If any server fails to initialize
   the stream, it will send an uncompressed ERROR message to its peer
   and close the connection.

   The data format used for the compression is described by RFC 1950
   [ZLIB], RFC 1951 [DEFLATE] and RFC 1952 [GZIP].

******* Anti abuse protections

   Most servers implement various kinds of protections against possible
   abusive behaviours from non trusted parties (typically users).  On
   some networks, such protections are indispensable, on others they are
   superfluous.  To require that all servers implement and enable such
   features on a particular network, the 'P' flag is used when two
   servers connect.  If this flag is present, it means that the server
   protections are enabled, and that the server REQUIRES all its server
   links to enable them as well.

   Commonly found protections are described in sections 5.7 (Tracking
   recently used nicknames) and 5.8 (Flood control of clients).

5.3.2 State information exchange when connecting

   The order of state information being exchanged between servers is
   essential.  The REQUIRED order is as follows:

           * all known servers;

           * all known client information;

           * all known channel information.

   Information regarding servers is sent via extra SERVER messages,
   client information with NICK and SERVICE messages and channels with
   NJOIN/MODE messages.

   NOTE: channel topics SHOULD NOT be exchanged here because the TOPIC
   command overwrites any old topic information, so at best, the two
   sides of the connection would exchange topics.

   By passing the state information about servers first, any collisions
   with servers that already exist occur before nickname collisions
   caused by a second server introducing a particular nickname.  Due to
   the IRC network only being able to exist as an acyclic graph, it may
   be possible that the network has already reconnected in another
   location.  In this event, the place where the server collision occurs
   indicates where the net needs to split.

5.4 Terminating server-client connections

   When a client connection unexpectedly closes, a QUIT message is
   generated on behalf of the client by the server to which the client
   was connected.  No other message is to be generated or used.

5.5 Terminating server-server connections

   If a server-server connection is closed, either via a SQUIT command
   or "natural" causes, the rest of the connected IRC network MUST have
   its information updated by the server which detected the closure.
   The terminating server then sends a list of SQUITs (one for each
   server behind that connection).  (See Section 4.1.6 (SQUIT)).

5.6 Tracking nickname changes

   All IRC servers are REQUIRED to keep a history of recent nickname
   changes.  This is important to allow the server to have a chance of
   keeping in touch of things when nick-change race conditions occur
   with commands manipulating them.  Messages which MUST trace nick
   changes are:

           * KILL (the nick being disconnected)

           * MODE (+/- o,v on channels)

           * KICK (the nick being removed from channel)

      No other commands need to check nick changes.

   In the above cases, the server is required to first check for the
   existence of the nickname, then check its history to see who that
   nick now belongs to (if anyone!).  This reduces the chances of race
   conditions but they can still occur with the server ending up
   affecting the wrong client.  When performing a change trace for an
   above command it is RECOMMENDED that a time range be given and
   entries which are too old ignored.

   For a reasonable history, a server SHOULD be able to keep previous
   nickname for every client it knows about if they all decided to
   change.  This size is limited by other factors (such as memory, etc).

5.7 Tracking recently used nicknames

   This mechanism is commonly known as "Nickname Delay", it has been
   proven to significantly reduce the number of nickname collisions
   resulting from "network splits"/reconnections as well as abuse.

   In addition of keeping track of nickname changes, servers SHOULD keep
   track of nicknames which were recently used and were released as the
   result of a "network split" or a KILL message.  These nicknames are
   then unavailable to the server local clients and cannot be re-used
   (even though they are not currently in use) for a certain period of
   time.

   The duration for which a nickname remains unavailable SHOULD be set
   considering many factors among which are the size (user wise) of the
   IRC network, and the usual duration of "network splits".  It SHOULD
   be uniform on all servers for a given IRC network.

5.8 Flood control of clients

   With a large network of interconnected IRC servers, it is quite easy
   for any single client attached to the network to supply a continuous
   stream of messages that result in not only flooding the network, but
   also degrading the level of service provided to others.  Rather than
   require every 'victim' to provide their own protection, flood
   protection was written into the server and is applied to all clients
   except services.  The current algorithm is as follows:

   * check to see if client's `message timer' is less than current time
     (set to be equal if it is);

   * read any data present from the client;

   * while the timer is less than ten (10) seconds ahead of the current
     time, parse any present messages and penalize the client by two (2)
     seconds for each message;

   * additional penalties MAY be used for specific commands which
     generate a lot of traffic across the network.

   This in essence means that the client may send one (1) message every
   two (2) seconds without being adversely affected.  Services MAY also
   be subject to this mechanism.

5.9 Non-blocking lookups

   In a real-time environment, it is essential that a server process
   does as little waiting as possible so that all the clients are
   serviced fairly.  Obviously this requires non-blocking IO on all
   network read/write operations.  For normal server connections, this
   was not difficult, but there are other support operations that may
   cause the server to block (such as disk reads).  Where possible, such
   activity SHOULD be performed with a short timeout.

5.9.1 Hostname (DNS) lookups

   Using the standard resolver libraries from Berkeley and others has
   meant large delays in some cases where replies have timed out.  To
   avoid this, a separate set of DNS routines were written for the
   current implementation.  Routines were setup for non-blocking IO
   operations with local cache, and then polled from within the main
   server IO loop.

5.9.2 Username (Ident) lookups

   Although there are numerous ident libraries (implementing the
   "Identification Protocol" [IDENT]) for use and inclusion into other
   programs, these caused problems since they operated in a synchronous
   manner and resulted in frequent delays.  Again the solution was to
   write a set of routines which would cooperate with the rest of the
   server and work using non-blocking IO.

6. Current problems

   There are a number of recognized problems with this protocol, all of
   which are hoped to be solved sometime in the near future during its
   rewrite.  Currently, work is underway to find working solutions to
   these problems.

6.1 Scalability

   It is widely recognized that this protocol does not scale
   sufficiently well when used in a large arena.  The main problem comes
   from the requirement that all servers know about all other servers
   and clients and that information regarding them be updated as soon as
   it changes.  It is also desirable to keep the number of servers low
   so that the path length between any two points is kept minimal and
   the spanning tree as strongly branched as possible.

6.2 Labels

   The current IRC protocol has 4 types of labels: the nickname, the
   channel name, the server name and the service name.  Each of the four
   types has its own domain and no duplicates are allowed inside that
   domain.  Currently, it is possible for users to pick the label for
   any of the first three, resulting in collisions.  It is widely
   recognized that this needs reworking, with a plan for unique names
   for nicks that don't collide being desirable as well as a solution
   allowing a cyclic tree.

6.2.1 Nicknames

   The idea of the nickname on IRC is very convenient for users to use
   when talking to each other outside of a channel, but there is only a
   finite nickname space and being what they are, it's not uncommon for
   several people to want to use the same nick.  If a nickname is chosen
   by two people using this protocol, either one will not succeed or
   both will be removed by use of KILL (See Section 3.7.1 of "IRC Client
   Protocol" [IRC-CLIENT]).

6.2.2 Channels

   The current channel layout requires that all servers know about all
   channels, their inhabitants and properties.  Besides not scaling
   well, the issue of privacy is also a concern.  A collision of
   channels is treated as an inclusive event (people from both nets on
   channel with common name are considered to be members of it) rather
   than an exclusive one such as used to solve nickname collisions.

   This protocol defines "Safe Channels" which are very unlikely to be
   the subject of a channel collision.  Other channel types are kept for
   backward compatibility.

6.2.3 Servers

   Although the number of servers is usually small relative to the
   number of users and channels, they too are currently REQUIRED to be
   known globally, either each one separately or hidden behind a mask.

6.3 Algorithms

   In some places within the server code, it has not been possible to
   avoid N^2 algorithms such as checking the channel list of a set of
   clients.

   In current server versions, there are only few database consistency
   checks, most of the time each server assumes that a neighbouring
   server is correct.  This opens the door to large problems if a
   connecting server is buggy or otherwise tries to introduce
   contradictions to the existing net.

   Currently, because of the lack of unique internal and global labels,
   there are a multitude of race conditions that exist.  These race
   conditions generally arise from the problem of it taking time for
   messages to traverse and effect the IRC network.  Even by changing to
   unique labels, there are problems with channel-related commands being
   disrupted.

7. Security Considerations

7.1 Authentication

   Servers only have two means of authenticating incoming connections:
   plain text password, and DNS lookups.  While these methods are weak
   and widely recognized as unsafe, their combination has proven to be
   sufficient in the past:

    * public networks typically allow user connections with only few
      restrictions, without requiring accurate authentication.

    * private networks which operate in a controlled environment often
      use home-grown authentication mechanisms not available on the
      internet: reliable ident servers [IDENT], or other proprietary
      mechanisms.

   The same comments apply to the authentication of IRC Operators.

   It should also be noted that while there has been no real demand over
   the years for stronger authentication, and no real effort to provide
   better means to safely authenticate users, the current protocol
   offers enough to be able to easily plug-in external authentication
   methods based on the information that a client can submit to the
   server upon connection: nickname, username, password.

7.2 Integrity

   Since the PASS and OPER messages of the IRC protocol are sent in
   clear text, a stream layer encryption mechanism (like "The TLS
   Protocol" [TLS]) could be used to protect these transactions.

8. Current support and availability

      Mailing lists for IRC related discussion:
        General discussion: <EMAIL>
        Protocol development: <EMAIL>

      Software implementations:
        ftp://ftp.irc.org/irc/server
        ftp://ftp.funet.fi/pub/unix/irc
        ftp://coombs.anu.edu.au/pub/irc

      Newsgroup: alt.irc

9. Acknowledgements

   Parts of this document were copied from the RFC 1459 [IRC] which
   first formally documented the IRC Protocol.  It has also benefited
   from many rounds of review and comments.  In particular, the
   following people have made significant contributions to this
   document:

   Matthew Green, Michael Neumayer, Volker Paulsen, Kurt Roeckx, Vesa
   Ruokonen, Magnus Tjernstrom, Stefan Zehl.

10. References

   [KEYWORDS]   Bradner, S., "Key words for use in RFCs to Indicate
                Requirement Levels", BCP 14, RFC 2119, March 1997.

   [ABNF]       Crocker, D. and P. Overell, "Augmented BNF for Syntax
                Specifications: ABNF", RFC 2234, November 1997.

   [IRC]        Oikarinen, J. and D. Reed, "Internet Relay Chat
                Protocol", RFC 1459, May 1993.

   [IRC-ARCH]   Kalt, C., "Internet Relay Chat: Architecture", RFC 2810,
                April 2000.

   [IRC-CLIENT] Kalt, C., "Internet Relay Chat: Client Protocol", RFC
                2812, April 2000.


   [IRC-CHAN]   Kalt, C., "Internet Relay Chat: Channel Management", RFC
                2811, April 2000.

   [ZLIB]       Deutsch, P. and J-L. Gailly, "ZLIB Compressed Data
                Format Specification version 3.3", RFC 1950, May 1996.

   [DEFLATE]    Deutsch, P., "DEFLATE Compressed Data Format
                Specification version 1.3", RFC 1951, May 1996.

   [GZIP]       Deutsch, P., "GZIP file format specification version
                4.3", RFC 1952, May 1996.

   [IDENT]      St. Johns, M., "The Identification Protocol", RFC 1413,
                February 1993.

   [TLS]        Dierks, T. and C. Allen, "The TLS Protocol", RFC 2246,
                January 1999.

11. Author's Address

   Christophe Kalt
   99 Teaneck Rd, Apt #117
   Ridgefield Park, NJ 07660
   USA

   EMail: <EMAIL>

12.  Full Copyright Statement

   Copyright (C) The Internet Society (2000).  All Rights Reserved.

   This document and translations of it may be copied and furnished to
   others, and derivative works that comment on or otherwise explain it
   or assist in its implementation may be prepared, copied, published
   and distributed, in whole or in part, without restriction of any
   kind, provided that the above copyright notice and this paragraph are
   included on all such copies and derivative works.  However, this
   document itself may not be modified in any way, such as by removing
   the copyright notice or references to the Internet Society or other
   Internet organizations, except as needed for the purpose of
   developing Internet standards in which case the procedures for
   copyrights defined in the Internet Standards process must be
   followed, or as required to translate it into languages other than
   English.

   The limited permissions granted above are perpetual and will not be
   revoked by the Internet Society or its successors or assigns.

   This document and the information contained herein is provided on an
   "AS IS" basis and THE INTERNET SOCIETY AND THE INTERNET ENGINEERING
   TASK FORCE DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING
   BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION
   HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF
   MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

Acknowledgement

   Funding for the RFC Editor function is currently provided by the
   Internet Society.